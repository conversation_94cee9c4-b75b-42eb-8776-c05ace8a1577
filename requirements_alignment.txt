# RLHF Alignment System Dependencies
# Install with: pip install -r requirements_alignment.txt

# Core ML libraries for embeddings and similarity search
sentence-transformers>=2.2.0
faiss-cpu>=1.7.0
scikit-learn>=1.0.0
numpy>=1.21.0

# Optional: GPU support for FAISS (uncomment if needed)
# faiss-gpu>=1.7.0

# Optional: Better embedding models (uncomment if needed)
# transformers>=4.20.0
# torch>=1.12.0
