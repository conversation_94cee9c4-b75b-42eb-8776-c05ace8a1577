#!/usr/bin/env python3
"""
Populate Alignment Requests from Existing Submissions
Creates alignment requests based on existing submissions in the database.
"""

import os
import sys
import json
import random
from datetime import datetime, timedelta
import logging

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, Submission, AlignmentRequest, Question, Part, User

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def analyze_submission_for_alignment(submission: Submission) -> tuple[bool, str, float]:
    """
    Analyze a submission to determine if it should generate an alignment request.
    
    Args:
        submission: Submission object to analyze
        
    Returns:
        Tuple of (should_create, trigger_reason, priority_score)
    """
    try:
        # Parse feedback JSON to get grading details
        feedback_data = json.loads(submission.feedback) if submission.feedback else {}
        
        score = submission.score
        part = submission.part
        max_score = part.score if part else 1
        
        # Calculate score percentage
        score_percentage = score / max_score if max_score > 0 else 0
        
        # Get evaluated points if available
        evaluated_points = feedback_data.get('evaluated_points', [])
        
        # Trigger conditions based on submission characteristics
        
        # 1. Edge cases - very low or very high scores
        if score_percentage <= 0.1 and len(submission.answer.strip()) > 20:
            return True, "low_score_edge_case", 0.8
        elif score_percentage >= 0.9 and max_score > 1:
            return True, "high_score_edge_case", 0.5
        
        # 2. Zero score cases with substantial answers
        if score == 0 and len(submission.answer.split()) > 10:
            return True, "zero_score_case", 0.9
        
        # 3. Perfect score cases
        if score == max_score and max_score > 2:
            return True, "perfect_score_case", 0.4
        
        # 4. Partial credit scenarios (if we have evaluated points)
        if evaluated_points:
            partial_points = [ep for ep in evaluated_points if ep.get('partial', False)]
            if len(partial_points) > 0:
                priority = 0.7 if len(partial_points) >= 2 else 0.5
                return True, "partial_credit_case", priority
        
        # 5. Complex answers (long text)
        word_count = len(submission.answer.split())
        if word_count > 100:
            return True, "complex_answer_case", 0.4
        elif word_count > 50:
            return True, "moderate_complexity_case", 0.3
        
        # 6. Mid-range scores that might be uncertain
        if 0.3 <= score_percentage <= 0.7 and max_score > 1:
            return True, "mid_range_uncertainty", 0.5
        
        # 7. Random sampling for baseline (reduced rate for existing data)
        if random.random() < 0.02:  # 2% for existing submissions
            return True, "random_sample", 0.3
        
        return False, "", 0.0
        
    except Exception as e:
        logger.error(f"Error analyzing submission {submission.id}: {e}")
        return False, "analysis_error", 0.0


def create_alignment_request_from_submission(submission: Submission, trigger_reason: str, priority_score: float) -> bool:
    """
    Create an alignment request from a submission.
    
    Args:
        submission: Submission object
        trigger_reason: Why this request was created
        priority_score: Priority score (0-1)
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Check if alignment request already exists
        existing_request = AlignmentRequest.query.filter_by(submission_id=submission.id).first()
        if existing_request:
            logger.debug(f"Alignment request already exists for submission {submission.id}")
            return False
        
        # Get related objects
        question = submission.question
        part = submission.part
        
        if not question or not part:
            logger.warning(f"Missing question or part for submission {submission.id}")
            return False
        
        # Create question context
        question_context = f"{question.title}: {part.description}" if question.title else part.description
        
        # Parse feedback to get AI decision details
        try:
            feedback_data = json.loads(submission.feedback) if submission.feedback else {}
        except json.JSONDecodeError:
            feedback_data = {}
        
        # Create alignment request
        alignment_request = AlignmentRequest(
            submission_id=submission.id,
            question_id=submission.question_id,
            part_id=submission.part_id,
            student_answer=submission.answer,
            expected_answer=part.answer,
            question_context=question_context,
            status='pending',
            priority_score=priority_score,
            trigger_reason=trigger_reason,
            created_at=submission.created_at  # Use submission's creation time
        )
        
        # Store AI decision details
        ai_decision = {
            'score': submission.score,
            'max_score': part.score,
            'evaluated_points': feedback_data.get('evaluated_points', []),
            'timing': feedback_data.get('timing', {}),
            'evidence_list': feedback_data.get('evidence_list', []),
            'detected_mistakes': feedback_data.get('detected_mistakes', []),
            'source': 'historical_submission'
        }
        
        alignment_request.set_ai_decision(ai_decision)
        
        db.session.add(alignment_request)
        return True
        
    except Exception as e:
        logger.error(f"Error creating alignment request for submission {submission.id}: {e}")
        return False


def populate_alignment_requests(limit: int = None, days_back: int = None, min_priority: float = 0.0) -> dict:
    """
    Populate alignment requests from existing submissions.
    
    Args:
        limit: Maximum number of alignment requests to create
        days_back: Only consider submissions from the last N days
        min_priority: Minimum priority score to include
        
    Returns:
        Dictionary with results
    """
    results = {
        'total_submissions': 0,
        'analyzed_submissions': 0,
        'created_requests': 0,
        'skipped_existing': 0,
        'errors': 0,
        'trigger_breakdown': {},
        'priority_breakdown': {'high': 0, 'medium': 0, 'low': 0}
    }
    
    with app.app_context():
        try:
            # Build query for submissions
            query = Submission.query.join(Part).join(Question)
            
            # Apply date filter if specified
            if days_back:
                cutoff_date = datetime.utcnow() - timedelta(days=days_back)
                query = query.filter(Submission.created_at >= cutoff_date)
            
            # Order by creation date (newest first)
            query = query.order_by(Submission.created_at.desc())
            
            # Apply limit if specified
            if limit:
                submissions = query.limit(limit * 3).all()  # Get extra to account for filtering
            else:
                submissions = query.all()
            
            results['total_submissions'] = len(submissions)
            logger.info(f"Found {len(submissions)} submissions to analyze")
            
            created_count = 0
            
            for submission in submissions:
                if limit and created_count >= limit:
                    break
                
                results['analyzed_submissions'] += 1
                
                # Analyze submission for alignment potential
                should_create, trigger_reason, priority_score = analyze_submission_for_alignment(submission)
                
                if not should_create or priority_score < min_priority:
                    continue
                
                # Create alignment request
                if create_alignment_request_from_submission(submission, trigger_reason, priority_score):
                    created_count += 1
                    results['created_requests'] += 1
                    
                    # Update breakdowns
                    results['trigger_breakdown'][trigger_reason] = results['trigger_breakdown'].get(trigger_reason, 0) + 1
                    
                    if priority_score >= 0.7:
                        results['priority_breakdown']['high'] += 1
                    elif priority_score >= 0.4:
                        results['priority_breakdown']['medium'] += 1
                    else:
                        results['priority_breakdown']['low'] += 1
                    
                    # Commit in batches
                    if created_count % 50 == 0:
                        db.session.commit()
                        logger.info(f"Created {created_count} alignment requests so far...")
                else:
                    results['errors'] += 1
            
            # Final commit
            db.session.commit()
            
            logger.info(f"Successfully created {results['created_requests']} alignment requests")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error in populate_alignment_requests: {e}")
            results['errors'] += 1
    
    return results


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Populate Alignment Requests from Submissions')
    parser.add_argument('--limit', type=int, default=None,
                       help='Maximum number of alignment requests to create')
    parser.add_argument('--days', type=int, default=None,
                       help='Only consider submissions from the last N days')
    parser.add_argument('--min-priority', type=float, default=0.0,
                       help='Minimum priority score to include (0.0-1.0)')
    parser.add_argument('--dry-run', action='store_true',
                       help='Analyze submissions but don\'t create alignment requests')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("Starting alignment request population...")
    logger.info(f"Parameters: limit={args.limit}, days_back={args.days}, min_priority={args.min_priority}")
    
    if args.dry_run:
        logger.info("DRY RUN MODE - No alignment requests will be created")
        # For dry run, we'll just analyze without creating
        with app.app_context():
            submissions = Submission.query.join(Part).join(Question).all()
            
            analysis_results = {
                'total_submissions': len(submissions),
                'would_create': 0,
                'trigger_breakdown': {},
                'priority_breakdown': {'high': 0, 'medium': 0, 'low': 0}
            }
            
            for submission in submissions:
                should_create, trigger_reason, priority_score = analyze_submission_for_alignment(submission)
                
                if should_create and priority_score >= args.min_priority:
                    analysis_results['would_create'] += 1
                    analysis_results['trigger_breakdown'][trigger_reason] = analysis_results['trigger_breakdown'].get(trigger_reason, 0) + 1
                    
                    if priority_score >= 0.7:
                        analysis_results['priority_breakdown']['high'] += 1
                    elif priority_score >= 0.4:
                        analysis_results['priority_breakdown']['medium'] += 1
                    else:
                        analysis_results['priority_breakdown']['low'] += 1
            
            print("\nDRY RUN RESULTS:")
            print(f"Total submissions analyzed: {analysis_results['total_submissions']}")
            print(f"Would create alignment requests: {analysis_results['would_create']}")
            print(f"Trigger breakdown: {analysis_results['trigger_breakdown']}")
            print(f"Priority breakdown: {analysis_results['priority_breakdown']}")
    else:
        results = populate_alignment_requests(
            limit=args.limit,
            days_back=args.days,
            min_priority=args.min_priority
        )
        
        print("\nPOPULATION RESULTS:")
        print(f"Total submissions: {results['total_submissions']}")
        print(f"Analyzed submissions: {results['analyzed_submissions']}")
        print(f"Created alignment requests: {results['created_requests']}")
        print(f"Errors: {results['errors']}")
        print(f"Trigger breakdown: {results['trigger_breakdown']}")
        print(f"Priority breakdown: {results['priority_breakdown']}")
        
        if results['created_requests'] > 0:
            print(f"\n✅ Successfully created {results['created_requests']} alignment requests!")
            print("You can now view them at /admin/alignment")
        else:
            print("\n⚠️  No alignment requests were created. Try adjusting the parameters.")


if __name__ == "__main__":
    main()
