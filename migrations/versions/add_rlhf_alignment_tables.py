"""Add RLHF alignment system tables

Revision ID: add_rlhf_alignment_tables
Revises: fbcde57ae16e
Create Date: 2025-01-27 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = 'add_rlhf_alignment_tables'
down_revision = 'fbcde57ae16e'
branch_labels = None
depends_on = None


def upgrade():
    # Create alignment_requests table
    op.create_table('alignment_requests',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('submission_id', sa.Integer(), nullable=False),
        sa.Column('question_id', sa.Integer(), nullable=False),
        sa.Column('part_id', sa.Integer(), nullable=False),
        sa.Column('marking_point_id', sa.Integer(), nullable=True),
        sa.Column('ai_decision', sa.Text(), nullable=False),
        sa.Column('student_answer', sa.Text(), nullable=False),
        sa.Column('expected_answer', sa.Text(), nullable=True),
        sa.Column('question_context', sa.Text(), nullable=True),
        sa.Column('status', sa.String(length=20), nullable=False),
        sa.Column('priority_score', sa.Float(), nullable=False),
        sa.Column('trigger_reason', sa.String(length=100), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('reviewed_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['marking_point_id'], ['marking_points.id'], name='fk_alignment_requests_marking_point_id'),
        sa.ForeignKeyConstraint(['part_id'], ['parts.id'], name='fk_alignment_requests_part_id'),
        sa.ForeignKeyConstraint(['question_id'], ['questions.id'], name='fk_alignment_requests_question_id'),
        sa.ForeignKeyConstraint(['submission_id'], ['submissions.id'], name='fk_alignment_requests_submission_id'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create teacher_feedback table
    op.create_table('teacher_feedback',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('alignment_request_id', sa.Integer(), nullable=False),
        sa.Column('teacher_id', sa.Integer(), nullable=False),
        sa.Column('corrected_score', sa.Float(), nullable=True),
        sa.Column('teacher_reasoning', sa.Text(), nullable=False),
        sa.Column('feedback_type', sa.String(length=50), nullable=False),
        sa.Column('severity', sa.String(length=20), nullable=False),
        sa.Column('general_guidance', sa.Text(), nullable=True),
        sa.Column('tags', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['alignment_request_id'], ['alignment_requests.id'], name='fk_teacher_feedback_alignment_request_id'),
        sa.ForeignKeyConstraint(['teacher_id'], ['users.id'], name='fk_teacher_feedback_teacher_id'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create alignment_embeddings table
    op.create_table('alignment_embeddings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('teacher_feedback_id', sa.Integer(), nullable=False),
        sa.Column('model_name', sa.String(length=100), nullable=False),
        sa.Column('vector_dimension', sa.Integer(), nullable=False),
        sa.Column('context_text', sa.Text(), nullable=False),
        sa.Column('feedback_text', sa.Text(), nullable=False),
        sa.Column('embedding_vector', sa.Text(), nullable=False),
        sa.Column('question_topic', sa.String(length=200), nullable=True),
        sa.Column('feedback_type', sa.String(length=50), nullable=False),
        sa.Column('severity', sa.String(length=20), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['teacher_feedback_id'], ['teacher_feedback.id'], name='fk_alignment_embeddings_teacher_feedback_id'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('teacher_feedback_id', 'model_name', name='unique_feedback_model_embedding')
    )


def downgrade():
    # Drop tables in reverse order due to foreign key constraints
    op.drop_table('alignment_embeddings')
    op.drop_table('teacher_feedback')
    op.drop_table('alignment_requests')
