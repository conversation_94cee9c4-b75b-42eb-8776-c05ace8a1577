# RLHF Alignment System

A comprehensive Reinforcement Learning from Human Feedback (RLHF) system that uses RAG (Retrieval-Augmented Generation) to dynamically adjust AI grading based on teacher feedback.

## Overview

This system creates a feedback loop where:
1. **AI grading decisions** are automatically flagged for teacher review based on various criteria
2. **Teachers provide corrections** through an intuitive web interface
3. **Feedback is stored as embeddings** in a RAG system for similarity search
4. **Future grading queries** the RAG system for relevant teacher feedback
5. **LLM prompts are enhanced** with retrieved feedback for better alignment

## Features

### 🎯 Intelligent Request Generation
- Automatic flagging of grading decisions that need review
- Multiple trigger criteria: low confidence, edge cases, random sampling, complex answers
- Priority scoring system for efficient teacher time allocation

### 👩‍🏫 Teacher Review Interface
- Clean, intuitive dashboard for reviewing AI grading decisions
- Side-by-side comparison of student answers and AI decisions
- Structured feedback forms with categorization and tagging
- Batch processing capabilities for efficient review

### 🔍 RAG-Powered Learning
- Automatic conversion of teacher feedback into searchable embeddings
- Similarity search to find relevant feedback for new grading decisions
- Context-aware retrieval based on question topics, student answers, and marking criteria
- Continuous learning from teacher corrections

### 📊 Analytics & Monitoring
- Comprehensive dashboard tracking system performance
- Feedback pattern analysis and common issue identification
- Teacher contribution metrics and system health monitoring
- Automated optimization and maintenance tools

## Installation

### Prerequisites
- Python 3.8+
- Flask application with SQLAlchemy
- Existing grading system (Gemini/Kimi integration)

### Quick Setup
```bash
# 1. Install alignment system dependencies
pip install -r requirements_alignment.txt

# 2. Run the automated setup script
python setup_alignment_system.py

# 3. Start your Flask application
python app.py
```

### Manual Setup
```bash
# 1. Install dependencies
pip install sentence-transformers faiss-cpu scikit-learn numpy

# 2. Run database migration
python manage_db.py upgrade

# 3. Initialize RAG system
python alignment_rag_system.py --build-index

# 4. Process any existing feedback
python feedback_processing_system.py --process-batch
```

## Usage

### For Administrators/Teachers

#### Accessing the Alignment Dashboard
1. Navigate to `/admin/alignment` in your application
2. View pending alignment requests sorted by priority
3. Click "Review" on any request to provide feedback

#### Providing Teacher Feedback
1. Review the AI's grading decision and reasoning
2. Provide corrected score (if needed)
3. Write detailed reasoning for the correction
4. Categorize the feedback type and severity
5. Add relevant tags for better organization

#### Monitoring System Performance
1. Visit `/admin/alignment/analytics` for comprehensive metrics
2. Monitor review rates, common issues, and system health
3. Use maintenance tools to optimize performance

### For Developers

#### Integration with Existing Grading
The system automatically integrates with your existing grading pipeline:

```python
# Alignment requests are automatically created during grading
# Teacher feedback is automatically retrieved and included in LLM prompts
# No changes needed to existing grading logic
```

#### Manual RAG Operations
```bash
# Process pending feedback into embeddings
python alignment_rag_system.py --process-feedback

# Search for similar feedback
python alignment_rag_system.py --search "student answer about photosynthesis"

# Analyze feedback patterns
python feedback_processing_system.py --analyze --days 30

# Generate comprehensive report
python feedback_processing_system.py --generate-report
```

## System Architecture

### Database Models
- **AlignmentRequest**: Stores grading decisions needing review
- **TeacherFeedback**: Stores teacher corrections and guidance
- **AlignmentEmbedding**: Stores RAG embeddings for similarity search

### Core Components
- **Alignment Routes** (`routes/alignment.py`): Web interface for teacher feedback
- **RAG System** (`alignment_rag_system.py`): Embedding generation and retrieval
- **Feedback Processor** (`feedback_processing_system.py`): Analysis and optimization
- **Integration Layer**: Automatic integration with existing grading pipeline

### Data Flow
1. **Grading Decision** → Automatic evaluation for alignment request creation
2. **Alignment Request** → Teacher review and feedback submission
3. **Teacher Feedback** → Automatic embedding generation and storage
4. **New Grading** → RAG retrieval of relevant feedback → Enhanced LLM prompt

## Configuration

### Trigger Criteria
Alignment requests are created based on:
- **Random Sampling**: 5% of all submissions for baseline monitoring
- **Edge Cases**: Very low (≤10%) or very high (≥90%) scores
- **Partial Credit**: Complex grading scenarios with mixed results
- **Zero Scores**: Non-trivial answers receiving zero points
- **Perfect Scores**: Multi-point questions receiving full marks
- **Complex Answers**: Long responses that may be difficult to grade

### RAG System Settings
- **Model**: `all-MiniLM-L6-v2` (configurable)
- **Similarity Threshold**: 0.4 minimum for relevance
- **Top-K Retrieval**: 3 most relevant feedback items
- **Context Window**: Question + answer + marking criteria

## Monitoring & Maintenance

### Key Metrics
- **Review Rate**: Percentage of requests reviewed by teachers
- **Embedding Coverage**: Percentage of feedback converted to embeddings
- **System Health**: Overall performance and efficiency indicators
- **Common Issues**: Patterns in teacher corrections

### Automated Maintenance
- **Batch Processing**: Automatic embedding generation for new feedback
- **Index Optimization**: Periodic FAISS index rebuilding
- **Performance Monitoring**: Automated health checks and alerts

### Manual Operations
```bash
# Optimize RAG system performance
python feedback_processing_system.py --optimize

# Rebuild all embeddings
python alignment_rag_system.py --process-feedback --force-recreate

# Generate detailed analytics report
python feedback_processing_system.py --generate-report --days 90
```

## API Endpoints

### Admin Interface
- `GET /admin/alignment` - Main alignment dashboard
- `GET /admin/alignment/<id>` - Detailed request review
- `POST /admin/alignment/<id>/feedback` - Submit teacher feedback
- `GET /admin/alignment/analytics` - Analytics dashboard

### System Operations
- `POST /admin/alignment/process-feedback` - Process pending feedback
- `POST /admin/alignment/optimize` - Optimize RAG system
- `GET /api/alignment/stats` - System statistics (JSON)

## Troubleshooting

### Common Issues

#### "No embeddings found to build index"
```bash
# Process existing feedback first
python alignment_rag_system.py --process-feedback
```

#### "Model loading failed"
```bash
# Ensure sentence-transformers is installed
pip install sentence-transformers
```

#### "Database migration failed"
```bash
# Check if tables exist
python manage_db.py status
# Run migration manually
python manage_db.py upgrade
```

### Performance Optimization
- Monitor embedding coverage in analytics dashboard
- Process feedback batches regularly
- Rebuild FAISS index periodically for optimal search performance
- Archive old alignment requests to maintain performance

## Contributing

### Adding New Trigger Criteria
Modify `should_create_alignment_request()` in `routes/api.py`:

```python
# Add new trigger condition
if your_condition:
    return True, "your_trigger_reason", priority_score
```

### Customizing RAG Retrieval
Modify `search_similar_feedback()` in `alignment_rag_system.py`:

```python
# Adjust similarity thresholds, top-k values, or filtering criteria
```

### Extending Analytics
Add new metrics to `analyze_feedback_patterns()` in `feedback_processing_system.py`.

## License

This RLHF Alignment System is part of the VAST educational platform and follows the same licensing terms.

## Support

For issues, questions, or contributions:
1. Check the troubleshooting section above
2. Review system logs for detailed error information
3. Use the analytics dashboard to monitor system health
4. Contact the development team for additional support
