#!/usr/bin/env python3
"""
Feedback Processing and Learning System
Processes teacher feedback to extract insights and continuously improve the alignment system.
"""

import os
import sys
import json
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import logging
from collections import defaultdict, Counter

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import (db, TeacherFeedback, AlignmentRequest, AlignmentEmbedding, 
                   Question, Part, Topic, Subject, MarkingPoint)
from alignment_rag_system import AlignmentRAGSystem

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class FeedbackProcessor:
    """Processes teacher feedback to extract insights and improve the system."""
    
    def __init__(self):
        self.rag_system = AlignmentRAGSystem()
    
    def analyze_feedback_patterns(self, days_back: int = 30) -> Dict:
        """
        Analyze patterns in teacher feedback over the specified time period.
        
        Args:
            days_back: Number of days to look back for analysis
            
        Returns:
            Dictionary with analysis results
        """
        with app.app_context():
            # Get recent feedback
            cutoff_date = datetime.utcnow() - timedelta(days=days_back)
            feedback_list = TeacherFeedback.query.filter(
                TeacherFeedback.created_at >= cutoff_date
            ).all()
            
            if not feedback_list:
                return {'error': 'No feedback found in the specified period'}
            
            analysis = {
                'total_feedback': len(feedback_list),
                'feedback_types': Counter(),
                'severity_distribution': Counter(),
                'topic_patterns': defaultdict(list),
                'score_corrections': [],
                'common_issues': [],
                'improvement_suggestions': []
            }
            
            # Analyze feedback patterns
            for feedback in feedback_list:
                analysis['feedback_types'][feedback.feedback_type] += 1
                analysis['severity_distribution'][feedback.severity] += 1
                
                # Get topic information
                alignment_request = feedback.alignment_request
                if alignment_request and alignment_request.question and alignment_request.question.topic:
                    topic_name = alignment_request.question.topic.name
                    analysis['topic_patterns'][topic_name].append({
                        'feedback_type': feedback.feedback_type,
                        'severity': feedback.severity,
                        'reasoning': feedback.teacher_reasoning[:100]
                    })
                
                # Analyze score corrections
                if feedback.corrected_score is not None:
                    ai_decision = alignment_request.get_ai_decision()
                    ai_score = ai_decision.get('score', 0) if ai_decision else 0
                    max_score = alignment_request.part.score if alignment_request.part else 1
                    
                    correction = {
                        'ai_score': ai_score,
                        'corrected_score': feedback.corrected_score,
                        'max_score': max_score,
                        'difference': feedback.corrected_score - ai_score,
                        'percentage_change': ((feedback.corrected_score - ai_score) / max_score * 100) if max_score > 0 else 0
                    }
                    analysis['score_corrections'].append(correction)
            
            # Extract common issues
            analysis['common_issues'] = self._extract_common_issues(feedback_list)
            
            # Generate improvement suggestions
            analysis['improvement_suggestions'] = self._generate_improvement_suggestions(analysis)
            
            return analysis
    
    def _extract_common_issues(self, feedback_list: List[TeacherFeedback]) -> List[Dict]:
        """Extract common issues from teacher feedback."""
        issue_keywords = {
            'too_harsh': ['harsh', 'strict', 'unfair', 'too low', 'undergraded'],
            'too_lenient': ['lenient', 'generous', 'too high', 'overgraded', 'easy'],
            'missed_concept': ['missed', 'overlooked', 'ignored', 'failed to recognize'],
            'wrong_interpretation': ['misinterpreted', 'misunderstood', 'incorrect interpretation'],
            'partial_credit': ['partial', 'some credit', 'half marks', 'partial understanding'],
            'edge_case': ['edge case', 'unusual', 'special case', 'exception']
        }
        
        issue_counts = defaultdict(list)
        
        for feedback in feedback_list:
            reasoning_lower = feedback.teacher_reasoning.lower()
            
            for issue_type, keywords in issue_keywords.items():
                for keyword in keywords:
                    if keyword in reasoning_lower:
                        issue_counts[issue_type].append({
                            'feedback_id': feedback.id,
                            'reasoning': feedback.teacher_reasoning[:150],
                            'severity': feedback.severity
                        })
                        break
        
        # Sort by frequency and return top issues
        common_issues = []
        for issue_type, examples in issue_counts.items():
            if len(examples) >= 2:  # Only include issues that appear multiple times
                common_issues.append({
                    'issue_type': issue_type,
                    'frequency': len(examples),
                    'examples': examples[:3],  # Top 3 examples
                    'severity_breakdown': Counter([ex['severity'] for ex in examples])
                })
        
        return sorted(common_issues, key=lambda x: x['frequency'], reverse=True)
    
    def _generate_improvement_suggestions(self, analysis: Dict) -> List[str]:
        """Generate improvement suggestions based on analysis."""
        suggestions = []
        
        # Score correction analysis
        if analysis['score_corrections']:
            corrections = analysis['score_corrections']
            avg_difference = sum(c['difference'] for c in corrections) / len(corrections)
            
            if avg_difference > 0.5:
                suggestions.append("AI tends to undergrade - consider adjusting prompts to be more lenient")
            elif avg_difference < -0.5:
                suggestions.append("AI tends to overgrade - consider adjusting prompts to be more strict")
        
        # Feedback type analysis
        feedback_types = analysis['feedback_types']
        if feedback_types.get('reasoning_improvement', 0) > feedback_types.get('score_correction', 0):
            suggestions.append("Focus on improving AI reasoning quality rather than just score accuracy")
        
        # Severity analysis
        severity_dist = analysis['severity_distribution']
        if severity_dist.get('high', 0) > len(analysis['total_feedback']) * 0.3:
            suggestions.append("High number of severe issues - consider retraining or prompt adjustments")
        
        # Topic-specific suggestions
        topic_patterns = analysis['topic_patterns']
        for topic, issues in topic_patterns.items():
            if len(issues) >= 3:
                suggestions.append(f"Topic '{topic}' has recurring issues - consider topic-specific training")
        
        return suggestions
    
    def process_feedback_batch(self, batch_size: int = 50) -> Dict:
        """
        Process a batch of unprocessed feedback into embeddings and insights.
        
        Args:
            batch_size: Number of feedback items to process
            
        Returns:
            Processing results
        """
        results = {
            'processed_count': 0,
            'embedding_count': 0,
            'errors': [],
            'insights': {}
        }
        
        try:
            # Process embeddings
            embedding_count = self.rag_system.process_pending_feedback()
            results['embedding_count'] = embedding_count
            
            # Rebuild FAISS index if new embeddings were created
            if embedding_count > 0:
                self.rag_system.build_faiss_index()
                logger.info(f"Rebuilt FAISS index with {embedding_count} new embeddings")
            
            # Analyze recent feedback patterns
            results['insights'] = self.analyze_feedback_patterns(days_back=7)
            
            results['processed_count'] = embedding_count
            
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            results['errors'].append(str(e))
        
        return results
    
    def generate_alignment_report(self, days_back: int = 30) -> Dict:
        """
        Generate a comprehensive alignment report.
        
        Args:
            days_back: Number of days to include in the report
            
        Returns:
            Comprehensive alignment report
        """
        with app.app_context():
            cutoff_date = datetime.utcnow() - timedelta(days=days_back)
            
            # Get statistics
            total_requests = AlignmentRequest.query.filter(
                AlignmentRequest.created_at >= cutoff_date
            ).count()
            
            reviewed_requests = AlignmentRequest.query.filter(
                AlignmentRequest.created_at >= cutoff_date,
                AlignmentRequest.status == 'reviewed'
            ).count()
            
            pending_requests = AlignmentRequest.query.filter(
                AlignmentRequest.status == 'pending'
            ).count()
            
            total_feedback = TeacherFeedback.query.filter(
                TeacherFeedback.created_at >= cutoff_date
            ).count()
            
            total_embeddings = AlignmentEmbedding.query.count()
            
            # Analyze feedback patterns
            feedback_analysis = self.analyze_feedback_patterns(days_back)
            
            # Calculate review rate
            review_rate = (reviewed_requests / total_requests * 100) if total_requests > 0 else 0
            
            report = {
                'period': f"Last {days_back} days",
                'generated_at': datetime.utcnow().isoformat(),
                'statistics': {
                    'total_alignment_requests': total_requests,
                    'reviewed_requests': reviewed_requests,
                    'pending_requests': pending_requests,
                    'review_rate_percentage': round(review_rate, 2),
                    'total_teacher_feedback': total_feedback,
                    'total_embeddings': total_embeddings
                },
                'feedback_analysis': feedback_analysis,
                'system_health': {
                    'embedding_coverage': (total_embeddings / total_feedback * 100) if total_feedback > 0 else 0,
                    'pending_backlog': pending_requests,
                    'processing_efficiency': 'Good' if review_rate > 80 else 'Needs Improvement'
                }
            }
            
            return report
    
    def optimize_rag_system(self) -> Dict:
        """
        Optimize the RAG system based on feedback patterns.
        
        Returns:
            Optimization results
        """
        results = {
            'actions_taken': [],
            'recommendations': [],
            'performance_metrics': {}
        }
        
        try:
            # Rebuild embeddings for better performance
            embedding_count = self.rag_system.process_pending_feedback(force_recreate=False)
            if embedding_count > 0:
                results['actions_taken'].append(f"Processed {embedding_count} new embeddings")
            
            # Rebuild FAISS index
            if self.rag_system.build_faiss_index():
                results['actions_taken'].append("Rebuilt FAISS index for improved search performance")
            
            # Analyze system performance
            with app.app_context():
                total_embeddings = AlignmentEmbedding.query.count()
                total_feedback = TeacherFeedback.query.count()
                
                results['performance_metrics'] = {
                    'total_embeddings': total_embeddings,
                    'total_feedback': total_feedback,
                    'embedding_coverage': (total_embeddings / total_feedback * 100) if total_feedback > 0 else 0
                }
                
                # Generate recommendations
                if total_embeddings < total_feedback:
                    results['recommendations'].append("Some feedback items are missing embeddings - run full reprocessing")
                
                if total_feedback < 10:
                    results['recommendations'].append("Limited feedback data - encourage more teacher reviews")
                
        except Exception as e:
            logger.error(f"Error optimizing RAG system: {e}")
            results['actions_taken'].append(f"Error: {str(e)}")
        
        return results


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Feedback Processing and Learning System')
    parser.add_argument('--analyze', action='store_true',
                       help='Analyze feedback patterns')
    parser.add_argument('--process-batch', action='store_true',
                       help='Process a batch of feedback')
    parser.add_argument('--generate-report', action='store_true',
                       help='Generate alignment report')
    parser.add_argument('--optimize', action='store_true',
                       help='Optimize RAG system')
    parser.add_argument('--days', type=int, default=30,
                       help='Number of days to analyze')
    
    args = parser.parse_args()
    
    processor = FeedbackProcessor()
    
    if args.analyze:
        analysis = processor.analyze_feedback_patterns(args.days)
        print(json.dumps(analysis, indent=2, default=str))
    
    if args.process_batch:
        results = processor.process_feedback_batch()
        print(f"Processed {results['processed_count']} feedback items")
        print(f"Created {results['embedding_count']} embeddings")
    
    if args.generate_report:
        report = processor.generate_alignment_report(args.days)
        print(json.dumps(report, indent=2, default=str))
    
    if args.optimize:
        results = processor.optimize_rag_system()
        print("Optimization results:")
        print(json.dumps(results, indent=2, default=str))


if __name__ == "__main__":
    main()
