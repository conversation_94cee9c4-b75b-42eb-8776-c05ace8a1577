{% extends 'base.html' %}

{% block title %}Admin - Add Question{% endblock %}

{% block head %}
{{ super() }}
<!-- Add required libraries for Markdown and LaTeX rendering -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
<script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>

<!-- Custom styles for LaTeX rendering -->
<style>
    /* Ensure inline LaTeX is properly aligned with text */
    .katex-inline {
        display: inline-block;
        vertical-align: middle;
    }

    /* Add some spacing around inline LaTeX */
    .live-preview .katex {
        margin: 0 0.1em;
    }

    /* Ensure display math has proper spacing */
    .live-preview .katex-display {
        margin: 1em 0;
    }

    /* Make sure inline LaTeX doesn't break the line flow */
    .live-preview p {
        display: block;
        line-height: 1.5;
        margin: 1em 0;
    }

    /* Ensure inline elements are properly aligned */
    .live-preview .katex-html {
        display: inline-block;
        vertical-align: middle;
    }

    /* Style for our custom inline math wrapper */
    .inline-math {
        display: inline;
        vertical-align: baseline;
        margin: 0 0.1em;
    }

    /* Ensure display math has proper spacing and alignment */
    .katex-display {
        margin: 1em 0;
        text-align: center;
    }

    /* Add some basic styling to the preview area */
    .live-preview {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        min-height: 50px;
        padding: 10px;
        overflow-wrap: break-word;
        background-color: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 0.375rem;
        margin-top: 0.5rem;
    }

    /* Style for code blocks in markdown */
    .live-preview pre {
        background-color: #f5f5f5;
        padding: 0.5em;
        border-radius: 0.25em;
        overflow-x: auto;
    }

    /* Style for inline code in markdown */
    .live-preview code {
        background-color: #f5f5f5;
        padding: 0.2em 0.4em;
        border-radius: 0.25em;
        font-family: monospace;
    }

    /* Style for paragraphs in the preview */
    .live-preview p {
        margin-bottom: 1em;
    }

    /* Style for headings in the preview */
    .live-preview h1, .live-preview h2, .live-preview h3,
    .live-preview h4, .live-preview h5, .live-preview h6 {
        margin-top: 1em;
        margin-bottom: 0.5em;
        font-weight: bold;
    }

    /* Style for lists in the preview */
    .live-preview ul, .live-preview ol {
        margin-left: 1.5em;
        margin-bottom: 1em;
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <div class="mx-auto max-w-3xl">
        <!-- Header -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
            <div class="p-6">
                <h1 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl text-center">Admin Dashboard</h1>
                <div class="mt-6 flex justify-center space-x-4">
                    <a href="{{ url_for('admin_dojo') }}"
                       class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        Manage Dojo
                    </a>
                    <a href="{{ url_for('bulk_upload') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        Bulk Upload
                    </a>
                    <a href="{{ url_for('admin_alignment') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        RLHF Alignment
                    </a>
                    <a href="{{ url_for('manage_notes_relevance') }}"
                       class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        Notes Relevance
                    </a>
                </div>
            </div>
        </div>

        <!-- Add Question Section -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden mb-6">
            <div class="p-6">
                <h2 class="text-2xl font-bold tracking-tight text-gray-900 text-center mb-6">Add New Question</h2>
            </div>
        </div>

        <!-- Question Form -->
        <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
            <div class="p-6">
                <form method="POST" action="{{ url_for('add_question') }}" enctype="multipart/form-data" class="space-y-6">
                    <!-- Subject and Topic Selection -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label for="subject" class="block text-sm font-medium leading-6 text-gray-900">Subject</label>
                            <div class="mt-2">
                                <select id="subject" name="subject_id" onchange="loadTopics(this.value)" required
                                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                    <option selected disabled>Select Subject</option>
                                    {% for subject in subjects %}
                                        <option value="{{ subject.id }}">{{ subject.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="topic" class="block text-sm font-medium leading-6 text-gray-900">Topic</label>
                            <div class="mt-2">
                                <select id="topic" name="topic_id" required
                                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                    <option selected disabled>Select Topic</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Question Title -->
                    <div>
                        <label for="title" class="block text-sm font-medium leading-6 text-gray-900">Question Title</label>
                        <div class="mt-2">
                            <textarea id="title" name="title" rows="2" required
                                class="latex-content block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"></textarea>
                        </div>
                    </div>


                    <!-- Source -->
                    <div>
                        <label for="source" class="block text-sm font-medium leading-6 text-gray-900">Source</label>
                        <div class="mt-2">
                            <textarea id="source" name="source" rows="2"
                                      class="latex-content block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"></textarea>
                        </div>
                    </div>

                    <!-- File Uploads -->
                    <!-- <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label for="attachment" class="block text-sm font-medium leading-6 text-gray-900">Attachments</label>
                            <div class="mt-2">
                                <input type="file" id="attachment" name="attachment"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                            </div>
                        </div>
                    </div> -->

                    <!-- Number of Parts -->
                    <div>
                        <label for="num_parts" class="block text-sm font-medium leading-6 text-gray-900">Number of Parts</label>
                        <div class="mt-2">
                            <input type="number" id="num_parts" name="num_parts" min="0" required
                                   class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                        </div>
                    </div>

                    <!-- Question Parts Container -->
                    <div id="question_parts" class="space-y-4"></div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit"
                                class="flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold leading-6 text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">
                            Add Question
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    function loadTopics(subjectId) {
        fetch('/get_topics/' + subjectId)
            .then(response => response.json())
            .then(data => {
                let topicSelect = document.getElementById('topic');
                topicSelect.innerHTML = "<option selected disabled>Select Topic</option>";
                data.topics.forEach(function(topic) {
                    let option = document.createElement("option");
                    option.value = topic.id;
                    option.text = topic.name;
                    topicSelect.appendChild(option);
                });
            });
    }

    document.getElementById('num_parts').addEventListener('input', function() {
        let numParts = this.value;
        let partsContainer = document.getElementById('question_parts');
        partsContainer.innerHTML = '';

        for (let i = 0; i < numParts; i++) {
            let partDiv = document.createElement('div');
            partDiv.className = 'bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden';
            partDiv.innerHTML = `
                <div class="p-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900">Part ${i + 1}</h3>
                        <button type="button" onclick="togglePart(${i})" class="text-gray-400 hover:text-gray-500">
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>
                    <div id="partContent${i}" class="space-y-4">
                        <div>
                            <label for="attachment_${i}" class="block text-sm font-medium leading-6 text-gray-900">Attachments</label>
                            <div class="mt-2">
                                <input type="file" id="attachment_${i}" name="attachment_${i}"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100">
                            </div>
                        </div>
                        <div>
                            <label for="input_type_${i}" class="block text-sm font-medium leading-6 text-gray-900">Input Type</label>
                            <div class="mt-2">
                                <select id="input_type_${i}" name="input_type_${i}" onchange="toggleInputType(${i})" required
                                        class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                                    <option value="saq" selected>Short Answer Question (SAQ)</option>
                                    <option value="mcq">Multiple Choice Question (MCQ)</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label for="part_${i}" class="block text-sm font-medium leading-6 text-gray-900">Description</label>
                            <div class="mt-2">
                                <textarea id="part_${i}" name="part_${i}" rows="2" required
                                          class="latex-content block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"></textarea>
                                <div id="preview-part-${i}-description" class="live-preview mt-2 p-3 border rounded-md bg-gray-50 min-h-[50px] prose prose-sm max-w-none"></div>
                                <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                                    <span>Supports Markdown and LaTeX (use $ for inline math, $$ for display math)</span>
                                    <button type="button" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs"
                                            onclick="document.getElementById('part_${i}').dispatchEvent(new Event('input'))">
                                        Refresh Preview
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- SAQ Answer Section (default) -->
                        <div id="saq_section_${i}" class="answer-section">
                            <label for="answer_${i}" class="block text-sm font-medium leading-6 text-gray-900">Answer</label>
                            <div class="mt-2">
                                <textarea id="answer_${i}" name="answer_${i}" rows="2" required
                                          class="latex-content block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"></textarea>
                                <div id="preview-part-${i}-answer" class="live-preview mt-2 p-3 border rounded-md bg-gray-50 min-h-[50px] prose prose-sm max-w-none"></div>
                                <div class="flex justify-between items-center text-xs text-gray-500 mt-1">
                                    <span>Supports Markdown and LaTeX (use $ for inline math, $$ for display math)</span>
                                    <button type="button" class="px-2 py-1 bg-gray-200 hover:bg-gray-300 rounded text-xs"
                                            onclick="document.getElementById('answer_${i}').dispatchEvent(new Event('input'))">
                                        Refresh Preview
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- MCQ Options Section (hidden by default) -->
                        <div id="mcq_section_${i}" class="answer-section hidden">
                            <div class="flex justify-between items-center mb-2">
                                <label class="block text-sm font-medium leading-6 text-gray-900">Multiple Choice Options</label>
                                <button type="button" onclick="addMcqOption(${i})" class="text-green-600 hover:text-green-800 text-sm">
                                    <i class="fas fa-plus"></i> Add Option
                                </button>
                            </div>
                            <div id="mcq_options_${i}" class="space-y-3">
                                <!-- Initial MCQ options will be added here -->
                                <div class="mcq-option">
                                    <div class="flex items-center gap-2 mb-1">
                                        <input type="radio" id="mcq_correct_${i}_0" name="mcq_correct_${i}" value="0" required
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500">
                                        <textarea name="mcq_option_${i}[]" required
                                               class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                               placeholder="Option text"></textarea>
                                        <button type="button" onclick="removeMcqOption(this, ${i})" class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <div class="live-preview ml-6 p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
                                </div>
                                <div class="mcq-option">
                                    <div class="flex items-center gap-2 mb-1">
                                        <input type="radio" id="mcq_correct_${i}_1" name="mcq_correct_${i}" value="1" required
                                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500">
                                        <textarea name="mcq_option_${i}[]" required
                                               class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                               placeholder="Option text"></textarea>
                                        <button type="button" onclick="removeMcqOption(this, ${i})" class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <div class="live-preview ml-6 p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
                                </div>
                            </div>
                            <p class="mt-1 text-sm text-gray-500">Select the radio button next to the correct answer.</p>
                        </div>
                        <div>
                            <label for="score_${i}" class="block text-sm font-medium leading-6 text-gray-900">Marks</label>
                            <div class="mt-2">
                                <input type="number" id="score_${i}" name="score_${i}" min="1" max="20" value="1" required
                                       class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6">
                            </div>
                        </div>
                        <div>
                            <div class="flex justify-between items-center">
                                <label class="block text-sm font-medium leading-6 text-gray-900">Marking Points</label>
                                <div class="flex space-x-2">
                                    <button type="button" class="text-blue-600 hover:text-blue-800 text-sm generate-marking-points-btn"
                                            onclick="generateMarkingPoints(${i})">
                                        <i class="fas fa-magic"></i> Generate
                                        <span class="ml-1 loading-spinner-${i} hidden"><i class="fas fa-spinner fa-spin"></i></span>
                                    </button>
                                    <button type="button" onclick="addMarkingPoint(${i})" class="text-green-600 hover:text-green-800 text-sm">
                                        <i class="fas fa-plus"></i> Add
                                    </button>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div id="marking_points_${i}" class="space-y-2">
                                    <div class="flex flex-col mb-3">
                                        <div class="flex gap-2 mb-1">
                                            <textarea name="marking_point_${i}[]" required
                                                   class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                   placeholder="Marking point description"></textarea>
                                            <input type="number" name="marking_score_${i}[]" step="0.1" min="0" value="1" required
                                                   class="w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                                   placeholder="Score">
                                            <button type="button" onclick="removeMarkingPoint(this)" class="text-red-600 hover:text-red-800">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                        <div class="live-preview p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
                                    </div>
                                </div>
                            </div>
                            <p class="mt-1 text-sm text-gray-500">Add specific criteria for awarding marks to student answers.</p>
                        </div>
                    </div>
                </div>
            `;
            partsContainer.appendChild(partDiv);
        }

        // We need to wait a moment for the DOM to update before initializing
        setTimeout(function() {
            // Initialize all textareas with the latex-content class
            setupLatexPreview();
        }, 100);
    });

    function togglePart(index) {
        const content = document.getElementById(`partContent${index}`);
        const button = content.previousElementSibling.querySelector('button');
        const icon = button.querySelector('i');

        if (content.classList.contains('hidden')) {
            content.classList.remove('hidden');
            icon.classList.remove('fa-chevron-up');
            icon.classList.add('fa-chevron-down');
        } else {
            content.classList.add('hidden');
            icon.classList.remove('fa-chevron-down');
            icon.classList.add('fa-chevron-up');
        }
    }

    function addMarkingPoint(partIndex) {
        const container = document.getElementById(`marking_points_${partIndex}`);
        const mpCount = container.querySelectorAll('.flex-col').length + 1;
        const div = document.createElement('div');
        div.className = 'flex flex-col mb-3';
        div.innerHTML = `
            <div class="flex gap-2 mb-1">
                <textarea name="marking_point_${partIndex}[]" required
                       class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                       placeholder="Marking point description"></textarea>
                <input type="number" name="marking_score_${partIndex}[]" value="1" step="0.1" min="0" required
                       class="w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                       placeholder="Score">
                <button type="button" onclick="removeMarkingPoint(this)" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div id="preview-part-${partIndex}-mp-${mpCount}" class="live-preview p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
        `;
        container.appendChild(div);

        // Initialize the preview for the new textarea
        setTimeout(function() {
            setupLatexPreview();
        }, 10);
    }

    function removeMarkingPoint(button) {
        button.closest('.flex-col').remove();
    }

    // Toggle between SAQ and MCQ input types
    function toggleInputType(partIndex) {
        const inputType = document.getElementById(`input_type_${partIndex}`).value;
        const saqSection = document.getElementById(`saq_section_${partIndex}`);
        const mcqSection = document.getElementById(`mcq_section_${partIndex}`);

        if (inputType === 'saq') {
            saqSection.classList.remove('hidden');
            mcqSection.classList.add('hidden');

            // Make SAQ answer required and MCQ options not required
            document.getElementById(`answer_${partIndex}`).required = true;
            const mcqOptions = document.querySelectorAll(`#mcq_options_${partIndex} textarea`);
            mcqOptions.forEach(option => {
                option.required = false;
            });

            // Make MCQ radio buttons not required
            const mcqRadios = document.querySelectorAll(`input[name="mcq_correct_${partIndex}"]`);
            mcqRadios.forEach(radio => {
                radio.required = false;
            });
        } else {
            saqSection.classList.add('hidden');
            mcqSection.classList.remove('hidden');

            // Make MCQ options required and SAQ answer not required
            document.getElementById(`answer_${partIndex}`).required = false;
            const mcqOptions = document.querySelectorAll(`#mcq_options_${partIndex} textarea`);
            mcqOptions.forEach(option => {
                option.required = true;
            });

            // Make at least one MCQ radio button required
            const mcqRadios = document.querySelectorAll(`input[name="mcq_correct_${partIndex}"]`);
            mcqRadios.forEach(radio => {
                radio.required = true;
            });
        }
    }

    // Add a new MCQ option
    function addMcqOption(partIndex) {
        const container = document.getElementById(`mcq_options_${partIndex}`);
        const optionCount = container.querySelectorAll('.mcq-option').length;

        const div = document.createElement('div');
        div.className = 'mcq-option';
        div.innerHTML = `
            <div class="flex items-center gap-2 mb-1">
                <input type="radio" id="mcq_correct_${partIndex}_${optionCount}" name="mcq_correct_${partIndex}" value="${optionCount}" required
                       class="h-4 w-4 text-indigo-600 focus:ring-indigo-500">
                <textarea name="mcq_option_${partIndex}[]" required
                       class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                       placeholder="Option text"></textarea>
                <button type="button" onclick="removeMcqOption(this, ${partIndex})" class="text-red-600 hover:text-red-800">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="live-preview ml-6 p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
        `;
        container.appendChild(div);

        // Initialize the preview for the new textarea
        setTimeout(function() {
            setupLatexPreview();
        }, 10);
    }

    // Remove an MCQ option
    function removeMcqOption(button, partIndex) {
        const optionElement = button.closest('.mcq-option');
        const container = document.getElementById(`mcq_options_${partIndex}`);

        // Don't allow removing if there are only 2 options left
        if (container.querySelectorAll('.mcq-option').length <= 2) {
            alert('A multiple choice question must have at least 2 options.');
            return;
        }

        // Remove the option
        optionElement.remove();

        // Update the value attributes of the radio buttons to match their new positions
        const options = container.querySelectorAll('.mcq-option');
        options.forEach((option, index) => {
            const radio = option.querySelector('input[type="radio"]');
            radio.id = `mcq_correct_${partIndex}_${index}`;
            radio.value = index;
        });
    }

    // Generate marking points via API
    async function generateMarkingPoints(partIndex) {
        const descriptionTextarea = document.getElementById(`part_${partIndex}`);
        const inputType = document.getElementById(`input_type_${partIndex}`).value;
        const scoreInput = document.getElementById(`score_${partIndex}`);
        const mpContainer = document.getElementById(`marking_points_${partIndex}`);
        const loadingSpinner = document.querySelector(`.loading-spinner-${partIndex}`);

        if (!descriptionTextarea || !scoreInput || !mpContainer) {
            console.error("Could not find necessary elements for part:", partIndex);
            alert("Error: Could not find elements for this part.");
            return;
        }

        const partDescription = descriptionTextarea.value;
        const partScore = scoreInput.value;
        let partAnswer = '';

        // Get the answer based on input type
        if (inputType === 'saq') {
            const answerTextarea = document.getElementById(`answer_${partIndex}`);
            if (!answerTextarea) {
                console.error("Could not find answer textarea for part:", partIndex);
                alert("Error: Could not find answer textarea for this part.");
                return;
            }
            partAnswer = answerTextarea.value;

            if (!partDescription || !partAnswer || !partScore) {
                alert("Please ensure the part description, answer, and score are filled before generating marking points.");
                return;
            }
        } else if (inputType === 'mcq') {
            // For MCQ, we'll use the correct option as the answer
            const correctOptionRadio = document.querySelector(`input[name="mcq_correct_${partIndex}"]:checked`);
            if (!correctOptionRadio) {
                alert("Please select a correct option for this multiple choice question.");
                return;
            }

            const correctOptionIndex = correctOptionRadio.value;
            const mcqOptions = document.querySelectorAll(`#mcq_options_${partIndex} textarea`);

            if (mcqOptions.length <= correctOptionIndex) {
                alert("Error: Selected correct option does not exist.");
                return;
            }

            partAnswer = mcqOptions[correctOptionIndex].value;

            if (!partDescription || !partAnswer || !partScore) {
                alert("Please ensure the part description, all options, and score are filled before generating marking points.");
                return;
            }
        }

        // Show loading state
        const generateButton = loadingSpinner.parentElement;
        generateButton.disabled = true;
        loadingSpinner.classList.remove('hidden');

        try {
            const response = await fetch('/generate_marking_points', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    partDescription: partDescription,
                    partAnswer: partAnswer,
                    partScore: parseInt(partScore) // Ensure score is sent as a number
                })
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ message: 'Unknown server error' }));
                throw new Error(`Server error: ${response.status} - ${errorData.message || 'Failed to generate marking points'}`);
            }

            const generatedPoints = await response.json();

            if (!Array.isArray(generatedPoints)) {
                throw new Error("Invalid response format from server. Expected an array.");
            }

            // Clear existing marking points
            mpContainer.innerHTML = '';

            // Add new marking points from response
            generatedPoints.forEach((mp, index) => {
                const div = document.createElement('div');
                div.className = 'flex flex-col mb-3';
                div.innerHTML = `
                    <div class="flex gap-2 mb-1">
                        <textarea name="marking_point_${partIndex}[]" required
                               class="latex-content flex-1 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                               placeholder="Marking point description">${mp.description || ''}</textarea>
                        <input type="number" name="marking_score_${partIndex}[]" value="${mp.score || 0}" step="0.1" min="0" required
                               class="w-24 rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                               placeholder="Score">
                        <button type="button" onclick="removeMarkingPoint(this)" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div id="preview-part-${partIndex}-mp-gen-${index}" class="live-preview p-2 border rounded-md bg-gray-50 min-h-[30px] prose prose-sm max-w-none text-sm"></div>
                `;
                mpContainer.appendChild(div);

                // We'll initialize all textareas after adding them to the DOM
                if (index === generatedPoints.length - 1) {
                    // Initialize the preview for all new textareas after the last one is added
                    setTimeout(function() {
                        setupLatexPreview();
                    }, 10);
                }
            });

        } catch (error) {
            console.error('Error generating marking points:', error);
            alert(`Failed to generate marking points: ${error.message}`);
        } finally {
            // Hide loading state
            generateButton.disabled = false;
            loadingSpinner.classList.add('hidden');
        }
    }

    // Main function to set up LaTeX preview for all textareas
    function setupLatexPreview() {
        console.log('Setting up LaTeX preview functionality');

        // Check if required libraries are loaded
        if (typeof marked === 'undefined' || typeof katex === 'undefined') {
            console.error('Required libraries not loaded. Retrying in 100ms...');
            setTimeout(setupLatexPreview, 100);
            return;
        }

        // Configure marked if not already configured
        if (!window.markedConfigured) {
            marked.use({
                breaks: true,  // Convert line breaks to <br>
                gfm: true,     // Enable GitHub Flavored Markdown
                mangle: false, // Don't mangle email addresses
                headerIds: false // Don't add IDs to headers
            });
            window.markedConfigured = true;
        }

        // Define the render function if not already defined
        if (!window.renderLatex) {
            // Function to render LaTeX with KaTeX
            window.renderLatex = function(latex, displayMode) {
                try {
                    const rendered = katex.renderToString(latex, {
                        displayMode: displayMode,
                        throwOnError: false,
                        output: 'html'
                    });

                    // For inline LaTeX, add a special class to help with styling
                    if (!displayMode) {
                        return `<span class="inline-math">${rendered}</span>`;
                    }
                    return rendered;
                } catch (error) {
                    console.error('Error rendering LaTeX:', error, latex);
                    return `<span class="text-red-500">Error rendering LaTeX: ${latex}</span>`;
                }
            };
        }

        // Process text containing both Markdown and LaTeX
        if (!window.renderLatexMarkdown) {
            window.renderLatexMarkdown = function(text) {
                if (!text) return '';

                try {
                    // Step 1: Extract and save LaTeX blocks
                    let placeholders = [];
                    let processedText = text;

                    // Extract display math ($$...$$)
                    processedText = processedText.replace(/\$\$(.*?)\$\$/gs, function(match, latex) {
                        const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
                        placeholders.push({
                            id: id,
                            latex: latex.trim(),
                            displayMode: true
                        });
                        return `<span id="${id}" class="latex-placeholder"></span>`;
                    });

                    // Extract inline math ($...$)
                    processedText = processedText.replace(/\$([^\$\n]+?)\$/g, function(match, latex) {
                        const id = `latex-${Math.random().toString(36).substring(2, 10)}`;
                        placeholders.push({
                            id: id,
                            latex: latex.trim(),
                            displayMode: false
                        });
                        return `<span id="${id}" class="latex-placeholder"></span>`;
                    });

                    // Step 2: Parse the text as Markdown
                    let html = marked.parse(processedText);

                    // Step 3: Replace placeholders with rendered LaTeX
                    placeholders.forEach(placeholder => {
                        const rendered = window.renderLatex(placeholder.latex, placeholder.displayMode);
                        html = html.replace(
                            new RegExp(`<span id="${placeholder.id}" class="latex-placeholder"></span>`, 'g'),
                            rendered
                        );
                    });

                    return html;
                } catch (error) {
                    console.error('Error in custom processing:', error);
                    return `<p class="text-red-500">Error processing text</p><pre>${text}</pre>`;
                }
            };
        }

        // Function to update the preview for a textarea
        if (!window.updatePreview) {
            window.updatePreview = function(textarea) {
                // Find the preview element
                let previewElement = null;

                // For marking points, the preview div has a special ID format
                if (textarea.name && textarea.name.includes('marking_point')) {
                    const mpContainer = textarea.closest('.flex-col');
                    if (mpContainer) {
                        previewElement = mpContainer.querySelector('.live-preview');
                    }
                }
                // For MCQ options
                else if (textarea.name && textarea.name.includes('mcq_option')) {
                    const optionContainer = textarea.closest('.mcq-option');
                    if (optionContainer) {
                        previewElement = optionContainer.querySelector('.live-preview');
                    }
                }
                else if (textarea.id) {
                    // For title and source
                    if (textarea.id === 'title') {
                        previewElement = document.getElementById('preview-title');
                    } else if (textarea.id === 'source') {
                        previewElement = document.getElementById('preview-source');
                    }
                    // For part descriptions
                    else if (textarea.id.startsWith('part_')) {
                        const partId = textarea.id.split('_')[1];
                        previewElement = document.getElementById(`preview-part-${partId}-description`);
                    }
                    // For part answers
                    else if (textarea.id.startsWith('answer_')) {
                        const partId = textarea.id.split('_')[1];
                        previewElement = document.getElementById(`preview-part-${partId}-answer`);
                    }
                }

                if (!previewElement) {
                    console.error('Preview element not found for textarea:', textarea);
                    return;
                }

                const text = textarea.value || '';
                const processedHTML = window.renderLatexMarkdown(text);
                previewElement.innerHTML = processedHTML || '<p class="text-gray-400 italic">Preview will appear here</p>';
            };
        }

        // Debounce function to limit update frequency
        function debounce(func, delay) {
            let timeoutId;
            return function(...args) {
                clearTimeout(timeoutId);
                timeoutId = setTimeout(() => {
                    func.apply(this, args);
                }, delay);
            };
        }

        // Create debounced version of updatePreview if not already created
        if (!window.debouncedUpdatePreview) {
            window.debouncedUpdatePreview = debounce(window.updatePreview, 300);
        }

        // Find all textareas with the latex-content class that don't have event listeners
        const textareas = document.querySelectorAll('.latex-content:not([data-preview-initialized])');
        console.log('Found textareas to initialize:', textareas.length);

        // Initialize each textarea
        textareas.forEach(textarea => {
            // Mark as initialized to avoid duplicate initialization
            textarea.setAttribute('data-preview-initialized', 'true');

            // Initial render
            window.updatePreview(textarea);

            // Add input event listener
            textarea.addEventListener('input', function() {
                window.debouncedUpdatePreview(this);
            });
        });
    }

    // Initialize live preview functionality when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the LaTeX preview system
        setupLatexPreview();
    });
</script>
{% endblock %}
