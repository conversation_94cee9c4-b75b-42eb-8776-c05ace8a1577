{% extends 'base.html' %}

{% block title %}Alignment Analytics Dashboard{% endblock %}

{% block head %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .chart-container {
        background: white;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .insight-card {
        background: #f8fafc;
        border-left: 4px solid #3b82f6;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 0 8px 8px 0;
    }
    
    .action-button {
        background: #10b981;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        cursor: pointer;
        margin-right: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .action-button:hover {
        background: #059669;
    }
    
    .status-good { color: #10b981; font-weight: bold; }
    .status-warning { color: #f59e0b; font-weight: bold; }
    .status-error { color: #ef4444; font-weight: bold; }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold tracking-tight text-gray-900">Alignment Analytics</h1>
            <p class="mt-2 text-gray-600">Monitor and analyze the RLHF alignment system performance</p>
        </div>
        <div class="flex gap-2">
            <a href="{{ url_for('admin_alignment') }}" 
               class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                ← Back to Alignment
            </a>
            <form method="GET" class="inline">
                <select name="days" onchange="this.form.submit()" class="border border-gray-300 rounded-md px-3 py-2">
                    <option value="7" {% if analytics.days_back == 7 %}selected{% endif %}>Last 7 days</option>
                    <option value="30" {% if analytics.days_back == 30 %}selected{% endif %}>Last 30 days</option>
                    <option value="90" {% if analytics.days_back == 90 %}selected{% endif %}>Last 90 days</option>
                </select>
            </form>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="metric-card">
            <div class="text-2xl font-bold">{{ analytics.report.statistics.total_alignment_requests }}</div>
            <div class="text-sm opacity-90">Total Requests</div>
        </div>
        <div class="metric-card">
            <div class="text-2xl font-bold">{{ analytics.report.statistics.review_rate_percentage }}%</div>
            <div class="text-sm opacity-90">Review Rate</div>
        </div>
        <div class="metric-card">
            <div class="text-2xl font-bold">{{ analytics.report.statistics.pending_requests }}</div>
            <div class="text-sm opacity-90">Pending Reviews</div>
        </div>
        <div class="metric-card">
            <div class="text-2xl font-bold">{{ analytics.report.statistics.total_embeddings }}</div>
            <div class="text-sm opacity-90">RAG Embeddings</div>
        </div>
    </div>

    <!-- System Health -->
    <div class="chart-container">
        <h2 class="text-xl font-semibold mb-4">System Health</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center">
                <div class="text-2xl font-bold 
                    {% if analytics.report.system_health.embedding_coverage >= 90 %}status-good
                    {% elif analytics.report.system_health.embedding_coverage >= 70 %}status-warning
                    {% else %}status-error{% endif %}">
                    {{ "%.1f"|format(analytics.report.system_health.embedding_coverage) }}%
                </div>
                <div class="text-sm text-gray-600">Embedding Coverage</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold 
                    {% if analytics.report.system_health.pending_backlog <= 10 %}status-good
                    {% elif analytics.report.system_health.pending_backlog <= 50 %}status-warning
                    {% else %}status-error{% endif %}">
                    {{ analytics.report.system_health.pending_backlog }}
                </div>
                <div class="text-sm text-gray-600">Pending Backlog</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold 
                    {% if analytics.report.system_health.processing_efficiency == 'Good' %}status-good
                    {% else %}status-warning{% endif %}">
                    {{ analytics.report.system_health.processing_efficiency }}
                </div>
                <div class="text-sm text-gray-600">Processing Efficiency</div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Trigger Reasons Chart -->
        <div class="chart-container">
            <h3 class="text-lg font-semibold mb-4">Alignment Request Triggers</h3>
            <canvas id="triggerChart" width="400" height="300"></canvas>
        </div>
        
        <!-- Priority Distribution Chart -->
        <div class="chart-container">
            <h3 class="text-lg font-semibold mb-4">Priority Distribution</h3>
            <canvas id="priorityChart" width="400" height="300"></canvas>
        </div>
    </div>

    <!-- Feedback Analysis -->
    {% if analytics.report.feedback_analysis and analytics.report.feedback_analysis.common_issues %}
    <div class="chart-container">
        <h3 class="text-lg font-semibold mb-4">Common Issues Identified</h3>
        <div class="space-y-3">
            {% for issue in analytics.report.feedback_analysis.common_issues[:5] %}
            <div class="insight-card">
                <div class="flex justify-between items-start">
                    <div>
                        <h4 class="font-medium text-gray-900">{{ issue.issue_type.replace('_', ' ').title() }}</h4>
                        <p class="text-sm text-gray-600 mt-1">Frequency: {{ issue.frequency }} occurrences</p>
                        <p class="text-sm text-gray-700 mt-2">{{ issue.examples[0].reasoning if issue.examples else 'No examples available' }}</p>
                    </div>
                    <div class="text-right">
                        <div class="text-sm text-gray-500">Severity Breakdown:</div>
                        {% for severity, count in issue.severity_breakdown.items() %}
                        <div class="text-xs">{{ severity.title() }}: {{ count }}</div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Improvement Suggestions -->
    {% if analytics.report.feedback_analysis and analytics.report.feedback_analysis.improvement_suggestions %}
    <div class="chart-container">
        <h3 class="text-lg font-semibold mb-4">Improvement Suggestions</h3>
        <ul class="space-y-2">
            {% for suggestion in analytics.report.feedback_analysis.improvement_suggestions %}
            <li class="flex items-start">
                <span class="text-blue-500 mr-2">•</span>
                <span class="text-gray-700">{{ suggestion }}</span>
            </li>
            {% endfor %}
        </ul>
    </div>
    {% endif %}

    <!-- Top Teachers -->
    {% if analytics.top_teachers %}
    <div class="chart-container">
        <h3 class="text-lg font-semibold mb-4">Top Contributing Teachers</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for teacher in analytics.top_teachers %}
            <div class="bg-gray-50 p-3 rounded-lg">
                <div class="font-medium">{{ teacher.username }}</div>
                <div class="text-sm text-gray-600">{{ teacher.feedback_count }} feedback items</div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- System Actions -->
    <div class="chart-container">
        <h3 class="text-lg font-semibold mb-4">System Maintenance</h3>
        <div class="flex flex-wrap gap-2">
            <form method="POST" action="{{ url_for('process_feedback_batch') }}" class="inline">
                <button type="submit" class="action-button">
                    Process Pending Feedback
                </button>
            </form>
            
            <form method="POST" action="{{ url_for('optimize_alignment_system') }}" class="inline">
                <button type="submit" class="action-button">
                    Optimize RAG System
                </button>
            </form>
            
            <a href="{{ url_for('alignment_analytics', days=7) }}" class="action-button">
                Refresh Analytics
            </a>
        </div>
        
        <div class="mt-4 text-sm text-gray-600">
            <p><strong>Process Pending Feedback:</strong> Convert teacher feedback into searchable embeddings</p>
            <p><strong>Optimize RAG System:</strong> Rebuild search index and optimize performance</p>
            <p><strong>Refresh Analytics:</strong> Update all metrics and charts</p>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="chart-container">
        <h3 class="text-lg font-semibold mb-4">Recent Activity (Last 7 Days)</h3>
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-3xl font-bold text-blue-600">{{ analytics.recent_activity.requests }}</div>
                <div class="text-sm text-gray-600">New Alignment Requests</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold text-green-600">{{ analytics.recent_activity.feedback }}</div>
                <div class="text-sm text-gray-600">Teacher Feedback Submitted</div>
            </div>
        </div>
    </div>
</div>

<script>
// Trigger Reasons Chart
const triggerCtx = document.getElementById('triggerChart').getContext('2d');
const triggerData = {{ analytics.trigger_reasons | tojson }};
new Chart(triggerCtx, {
    type: 'doughnut',
    data: {
        labels: triggerData.map(item => item.reason.replace('_', ' ')),
        datasets: [{
            data: triggerData.map(item => item.count),
            backgroundColor: [
                '#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Priority Distribution Chart
const priorityCtx = document.getElementById('priorityChart').getContext('2d');
const priorityData = {{ analytics.priority_distribution | tojson }};
new Chart(priorityCtx, {
    type: 'bar',
    data: {
        labels: priorityData.map(item => item.range),
        datasets: [{
            label: 'Requests',
            data: priorityData.map(item => item.count),
            backgroundColor: ['#ef4444', '#f59e0b', '#10b981']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
{% endblock %}
