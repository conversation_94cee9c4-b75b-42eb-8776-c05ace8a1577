{% extends 'base.html' %}

{% block title %}Alignment Review Dashboard{% endblock %}

{% block head %}
{{ super() }}
<style>
    .priority-high { border-left: 4px solid #dc2626; }
    .priority-medium { border-left: 4px solid #f59e0b; }
    .priority-low { border-left: 4px solid #10b981; }
    
    .status-pending { background-color: #fef3c7; color: #92400e; }
    .status-reviewed { background-color: #d1fae5; color: #065f46; }
    .status-dismissed { background-color: #f3f4f6; color: #6b7280; }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .filter-section {
        background: #f8fafc;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold tracking-tight text-gray-900">RLHF Alignment Review</h1>
            <p class="mt-2 text-gray-600">Review AI grading decisions and provide feedback for continuous improvement</p>
        </div>
        <a href="{{ url_for('alignment_analytics') }}"
           class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            📊 Analytics Dashboard
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div class="stats-card">
            <div class="text-2xl font-bold">{{ stats.total_pending }}</div>
            <div class="text-sm opacity-90">Pending Review</div>
        </div>
        <div class="stats-card">
            <div class="text-2xl font-bold">{{ stats.total_reviewed }}</div>
            <div class="text-sm opacity-90">Reviewed</div>
        </div>
        <div class="stats-card">
            <div class="text-2xl font-bold">{{ stats.high_priority }}</div>
            <div class="text-sm opacity-90">High Priority</div>
        </div>
        <div class="stats-card">
            <div class="text-2xl font-bold">{{ stats.total_pending + stats.total_reviewed }}</div>
            <div class="text-sm opacity-90">Total Requests</div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <div>
                <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" id="status" class="border border-gray-300 rounded-md px-3 py-2">
                    <option value="all" {% if status_filter == 'all' %}selected{% endif %}>All</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending</option>
                    <option value="reviewed" {% if status_filter == 'reviewed' %}selected{% endif %}>Reviewed</option>
                    <option value="dismissed" {% if status_filter == 'dismissed' %}selected{% endif %}>Dismissed</option>
                </select>
            </div>
            
            <div>
                <label for="priority" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
                <select name="priority" id="priority" class="border border-gray-300 rounded-md px-3 py-2">
                    <option value="all" {% if priority_filter == 'all' %}selected{% endif %}>All</option>
                    <option value="high" {% if priority_filter == 'high' %}selected{% endif %}>High (≥0.7)</option>
                    <option value="medium" {% if priority_filter == 'medium' %}selected{% endif %}>Medium (0.3-0.7)</option>
                    <option value="low" {% if priority_filter == 'low' %}selected{% endif %}>Low (<0.3)</option>
                </select>
            </div>
            
            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                Apply Filters
            </button>
        </form>
    </div>

    <!-- Alignment Requests List -->
    <div class="bg-white shadow-sm ring-1 ring-gray-900/5 rounded-lg overflow-hidden">
        {% if alignment_requests.items %}
            <div class="divide-y divide-gray-200">
                {% for request in alignment_requests.items %}
                <div class="p-6 priority-{{ 'high' if request.priority_score >= 0.7 else 'medium' if request.priority_score >= 0.3 else 'low' }}">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center gap-3 mb-2">
                                <h3 class="text-lg font-semibold text-gray-900">
                                    Request #{{ request.id }}
                                </h3>
                                <span class="px-2 py-1 text-xs font-medium rounded-full status-{{ request.status }}">
                                    {{ request.status.title() }}
                                </span>
                                <span class="text-sm text-gray-500">
                                    Priority: {{ "%.2f"|format(request.priority_score) }}
                                </span>
                            </div>
                            
                            <div class="text-sm text-gray-600 mb-3">
                                <div><strong>Question:</strong> {{ request.question.title or 'Untitled Question' }}</div>
                                <div><strong>Part:</strong> {{ request.part.description[:100] }}{% if request.part.description|length > 100 %}...{% endif %}</div>
                                <div><strong>Trigger:</strong> {{ request.trigger_reason.replace('_', ' ').title() }}</div>
                                <div><strong>Created:</strong> {{ request.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                                {% if request.reviewed_at %}
                                <div><strong>Reviewed:</strong> {{ request.reviewed_at.strftime('%Y-%m-%d %H:%M') }}</div>
                                {% endif %}
                            </div>
                            
                            <div class="text-sm">
                                <div class="mb-2">
                                    <strong>Student Answer:</strong>
                                    <div class="bg-gray-50 p-2 rounded mt-1">
                                        {{ request.student_answer[:200] }}{% if request.student_answer|length > 200 %}...{% endif %}
                                    </div>
                                </div>
                                
                                {% set ai_decision = request.get_ai_decision() %}
                                {% if ai_decision %}
                                <div>
                                    <strong>AI Decision:</strong>
                                    <div class="bg-blue-50 p-2 rounded mt-1">
                                        Score: {{ ai_decision.get('score', 'N/A') }}/{{ request.part.score }}
                                        {% if ai_decision.get('confidence') %}
                                        | Confidence: {{ "%.2f"|format(ai_decision.confidence) }}
                                        {% endif %}
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="ml-6 flex flex-col gap-2">
                            <a href="{{ url_for('alignment_request_detail', request_id=request.id) }}" 
                               class="bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 text-center">
                                {% if request.status == 'pending' %}Review{% else %}View{% endif %}
                            </a>
                            
                            {% if request.status == 'pending' %}
                            <form method="POST" action="{{ url_for('dismiss_alignment_request', request_id=request.id) }}" 
                                  onsubmit="return confirm('Are you sure you want to dismiss this request?')">
                                <button type="submit" 
                                        class="bg-gray-500 text-white px-4 py-2 rounded-md text-sm hover:bg-gray-600 w-full">
                                    Dismiss
                                </button>
                            </form>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if alignment_requests.pages > 1 %}
            <div class="bg-gray-50 px-6 py-3 border-t">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Showing {{ alignment_requests.per_page * (alignment_requests.page - 1) + 1 }} to 
                        {{ alignment_requests.per_page * alignment_requests.page if alignment_requests.page < alignment_requests.pages else alignment_requests.total }} 
                        of {{ alignment_requests.total }} results
                    </div>
                    
                    <div class="flex gap-2">
                        {% if alignment_requests.has_prev %}
                        <a href="{{ url_for('admin_alignment', page=alignment_requests.prev_num, status=status_filter, priority=priority_filter) }}" 
                           class="bg-white border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-50">
                            Previous
                        </a>
                        {% endif %}
                        
                        {% if alignment_requests.has_next %}
                        <a href="{{ url_for('admin_alignment', page=alignment_requests.next_num, status=status_filter, priority=priority_filter) }}" 
                           class="bg-white border border-gray-300 text-gray-700 px-3 py-2 rounded-md text-sm hover:bg-gray-50">
                            Next
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        {% else %}
            <div class="p-12 text-center">
                <div class="text-gray-500 text-lg">No alignment requests found</div>
                <div class="text-gray-400 text-sm mt-2">
                    {% if status_filter != 'all' or priority_filter != 'all' %}
                    Try adjusting your filters or 
                    <a href="{{ url_for('admin_alignment') }}" class="text-blue-600 hover:text-blue-800">view all requests</a>
                    {% else %}
                    Alignment requests will appear here when the system identifies grading decisions that need review.
                    {% endif %}
                </div>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}
