{% extends 'base.html' %}

{% block title %}Review Request #{{ alignment_request.id }}{% endblock %}

{% block head %}
{{ super() }}
<style>
    .context-section {
        background: #f8fafc;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .ai-decision-box {
        background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
        color: white;
        border-radius: 8px;
        padding: 1.5rem;
    }
    
    .student-answer-box {
        background: #fef3c7;
        border-left: 4px solid #f59e0b;
        padding: 1.5rem;
        border-radius: 0 8px 8px 0;
    }
    
    .feedback-form {
        background: white;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        padding: 2rem;
        margin-top: 2rem;
    }
    
    .priority-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .priority-high { background-color: #dc2626; }
    .priority-medium { background-color: #f59e0b; }
    .priority-low { background-color: #10b981; }
</style>
{% endblock %}

{% block content %}
<div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold tracking-tight text-gray-900">
                    <span class="priority-indicator priority-{{ 'high' if alignment_request.priority_score >= 0.7 else 'medium' if alignment_request.priority_score >= 0.3 else 'low' }}"></span>
                    Review Request #{{ alignment_request.id }}
                </h1>
                <p class="mt-2 text-gray-600">
                    Priority: {{ "%.2f"|format(alignment_request.priority_score) }} | 
                    Trigger: {{ alignment_request.trigger_reason.replace('_', ' ').title() }} |
                    Created: {{ alignment_request.created_at.strftime('%Y-%m-%d %H:%M') }}
                </p>
            </div>
            <a href="{{ url_for('admin_alignment') }}" 
               class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                ← Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Question Context -->
    <div class="context-section">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Question Context</h2>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div>
                <h3 class="font-medium text-gray-900 mb-2">Question Details</h3>
                <div class="text-sm space-y-1">
                    <div><strong>Title:</strong> {{ question.title or 'Untitled Question' }}</div>
                    {% if subject %}<div><strong>Subject:</strong> {{ subject.name }}</div>{% endif %}
                    {% if topic %}<div><strong>Topic:</strong> {{ topic.name }}</div>{% endif %}
                    <div><strong>Part Score:</strong> {{ part.score }} marks</div>
                </div>
                
                {% if question.description %}
                <div class="mt-4">
                    <h4 class="font-medium text-gray-900 mb-2">Question Description</h4>
                    <div class="text-sm bg-white p-3 rounded border">
                        {{ question.description[:300] }}{% if question.description|length > 300 %}...{% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
            
            <div>
                <h3 class="font-medium text-gray-900 mb-2">Part Details</h3>
                <div class="text-sm bg-white p-3 rounded border">
                    {{ part.description }}
                </div>
                
                {% if part.answer %}
                <div class="mt-4">
                    <h4 class="font-medium text-gray-900 mb-2">Expected Answer</h4>
                    <div class="text-sm bg-green-50 p-3 rounded border border-green-200">
                        {{ part.answer }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Student Answer vs AI Decision -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- Student Answer -->
        <div>
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Student Answer</h2>
            <div class="student-answer-box">
                <div class="text-sm">
                    {{ alignment_request.student_answer }}
                </div>
            </div>
        </div>
        
        <!-- AI Decision -->
        <div>
            <h2 class="text-xl font-semibold text-gray-900 mb-4">AI Grading Decision</h2>
            <div class="ai-decision-box">
                {% set ai_decision = alignment_request.get_ai_decision() %}
                <div class="space-y-3">
                    <div class="text-lg font-semibold">
                        Score: {{ ai_decision.get('score', 'N/A') }}/{{ part.score }}
                        {% if ai_decision.get('confidence') %}
                        <span class="text-sm font-normal opacity-90">
                            (Confidence: {{ "%.1f"|format(ai_decision.confidence * 100) }}%)
                        </span>
                        {% endif %}
                    </div>
                    
                    {% if ai_decision.get('reasoning') %}
                    <div class="text-sm opacity-90">
                        <strong>AI Reasoning:</strong><br>
                        {{ ai_decision.reasoning }}
                    </div>
                    {% endif %}
                    
                    {% if ai_decision.get('marking_point_results') %}
                    <div class="text-sm">
                        <strong>Marking Point Results:</strong>
                        <ul class="mt-2 space-y-1">
                            {% for mp in ai_decision.marking_point_results %}
                            <li class="flex justify-between">
                                <span>{{ mp.description[:50] }}{% if mp.description|length > 50 %}...{% endif %}</span>
                                <span>{{ mp.achieved_score }}/{{ mp.score }}</span>
                            </li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Teacher Feedback Form -->
    {% if alignment_request.status == 'pending' or existing_feedback %}
    <div class="feedback-form">
        <h2 class="text-xl font-semibold text-gray-900 mb-6">
            {% if existing_feedback %}Update Teacher Feedback{% else %}Provide Teacher Feedback{% endif %}
        </h2>
        
        <form method="POST" action="{{ url_for('submit_teacher_feedback', request_id=alignment_request.id) }}">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Left Column -->
                <div class="space-y-4">
                    <div>
                        <label for="corrected_score" class="block text-sm font-medium text-gray-700 mb-2">
                            Corrected Score (optional)
                        </label>
                        <input type="number" 
                               id="corrected_score" 
                               name="corrected_score" 
                               step="0.1" 
                               min="0" 
                               max="{{ part.score }}"
                               value="{% if existing_feedback and existing_feedback.corrected_score %}{{ existing_feedback.corrected_score }}{% endif %}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                               placeholder="Enter corrected score">
                        <div class="text-xs text-gray-500 mt-1">Max score: {{ part.score }}</div>
                    </div>
                    
                    <div>
                        <label for="feedback_type" class="block text-sm font-medium text-gray-700 mb-2">
                            Feedback Type
                        </label>
                        <select id="feedback_type" name="feedback_type" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="score_correction" {% if existing_feedback and existing_feedback.feedback_type == 'score_correction' %}selected{% endif %}>Score Correction</option>
                            <option value="reasoning_improvement" {% if existing_feedback and existing_feedback.feedback_type == 'reasoning_improvement' %}selected{% endif %}>Reasoning Improvement</option>
                            <option value="edge_case" {% if existing_feedback and existing_feedback.feedback_type == 'edge_case' %}selected{% endif %}>Edge Case</option>
                            <option value="concept_clarification" {% if existing_feedback and existing_feedback.feedback_type == 'concept_clarification' %}selected{% endif %}>Concept Clarification</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="severity" class="block text-sm font-medium text-gray-700 mb-2">
                            Severity
                        </label>
                        <select id="severity" name="severity" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="low" {% if existing_feedback and existing_feedback.severity == 'low' %}selected{% endif %}>Low</option>
                            <option value="medium" {% if existing_feedback and existing_feedback.severity == 'medium' %}selected{% endif %}>Medium</option>
                            <option value="high" {% if existing_feedback and existing_feedback.severity == 'high' %}selected{% endif %}>High</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
                            Tags (comma-separated)
                        </label>
                        <input type="text" 
                               id="tags" 
                               name="tags" 
                               value="{% if existing_feedback %}{{ existing_feedback.get_tags()|join(', ') }}{% endif %}"
                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                               placeholder="e.g., chemistry, stoichiometry, calculation">
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="space-y-4">
                    <div>
                        <label for="teacher_reasoning" class="block text-sm font-medium text-gray-700 mb-2">
                            Teacher Reasoning <span class="text-red-500">*</span>
                        </label>
                        <textarea id="teacher_reasoning" 
                                  name="teacher_reasoning" 
                                  rows="6" 
                                  required
                                  class="w-full border border-gray-300 rounded-md px-3 py-2"
                                  placeholder="Explain why the AI decision was incorrect and what the correct assessment should be...">{% if existing_feedback %}{{ existing_feedback.teacher_reasoning }}{% endif %}</textarea>
                    </div>
                    
                    <div>
                        <label for="general_guidance" class="block text-sm font-medium text-gray-700 mb-2">
                            General Guidance (optional)
                        </label>
                        <textarea id="general_guidance" 
                                  name="general_guidance" 
                                  rows="4" 
                                  class="w-full border border-gray-300 rounded-md px-3 py-2"
                                  placeholder="Provide general guidance for similar cases in the future...">{% if existing_feedback %}{{ existing_feedback.general_guidance }}{% endif %}</textarea>
                    </div>
                </div>
            </div>
            
            <div class="flex justify-between items-center mt-6 pt-6 border-t">
                <div class="flex gap-3">
                    <button type="submit" 
                            class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">
                        {% if existing_feedback %}Update Feedback{% else %}Submit Feedback{% endif %}
                    </button>
                    
                    {% if alignment_request.status == 'pending' %}
                    <button type="button" 
                            onclick="if(confirm('Are you sure you want to dismiss this request?')) { window.location.href='{{ url_for('dismiss_alignment_request', request_id=alignment_request.id) }}'; }"
                            class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600">
                        Dismiss Request
                    </button>
                    {% endif %}
                </div>
                
                <div class="text-sm text-gray-500">
                    <span class="text-red-500">*</span> Required fields
                </div>
            </div>
        </form>
    </div>
    {% endif %}

    <!-- Existing Feedback Display (if reviewed) -->
    {% if existing_feedback and alignment_request.status == 'reviewed' %}
    <div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-green-900 mb-4">Teacher Feedback</h3>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 text-sm">
            <div>
                <div class="mb-2"><strong>Corrected Score:</strong> {{ existing_feedback.corrected_score or 'Not provided' }}</div>
                <div class="mb-2"><strong>Feedback Type:</strong> {{ existing_feedback.feedback_type.replace('_', ' ').title() }}</div>
                <div class="mb-2"><strong>Severity:</strong> {{ existing_feedback.severity.title() }}</div>
                {% if existing_feedback.get_tags() %}
                <div class="mb-2"><strong>Tags:</strong> {{ existing_feedback.get_tags()|join(', ') }}</div>
                {% endif %}
            </div>
            <div>
                <div class="mb-2"><strong>Teacher:</strong> {{ existing_feedback.teacher.username }}</div>
                <div class="mb-2"><strong>Created:</strong> {{ existing_feedback.created_at.strftime('%Y-%m-%d %H:%M') }}</div>
                {% if existing_feedback.updated_at != existing_feedback.created_at %}
                <div class="mb-2"><strong>Updated:</strong> {{ existing_feedback.updated_at.strftime('%Y-%m-%d %H:%M') }}</div>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-4">
            <div class="mb-2"><strong>Teacher Reasoning:</strong></div>
            <div class="bg-white p-3 rounded border">{{ existing_feedback.teacher_reasoning }}</div>
        </div>
        
        {% if existing_feedback.general_guidance %}
        <div class="mt-4">
            <div class="mb-2"><strong>General Guidance:</strong></div>
            <div class="bg-white p-3 rounded border">{{ existing_feedback.general_guidance }}</div>
        </div>
        {% endif %}
    </div>
    {% endif %}
</div>
{% endblock %}
