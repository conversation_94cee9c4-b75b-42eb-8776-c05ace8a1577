#!/usr/bin/env python3
"""
RLHF Alignment RAG System
Handles embedding generation and retrieval for teacher feedback to improve AI grading alignment.
"""

import os
import sys
import numpy as np
from typing import List, Dict, Tuple, Optional
import logging
from datetime import datetime
import json

# Add the current directory to Python path for imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, TeacherFeedback, AlignmentEmbedding, AlignmentRequest, Question, Part, Topic

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

try:
    from sentence_transformers import SentenceTransformer
    import faiss
    from sklearn.preprocessing import normalize
except ImportError as e:
    logger.error(f"Required packages not installed: {e}")
    logger.error("Please install: pip install sentence-transformers faiss-cpu scikit-learn")
    sys.exit(1)


class AlignmentRAGSystem:
    """RAG system for teacher feedback embeddings and retrieval."""
    
    def __init__(self, model_name: str = 'all-MiniLM-L6-v2'):
        """
        Initialize the alignment RAG system.
        
        Args:
            model_name: Name of the sentence transformer model to use
        """
        self.model_name = model_name
        self.model = None
        self.faiss_index = None
        self.index_to_feedback_id = {}
        
    def load_model(self):
        """Load the sentence transformer model."""
        try:
            self.model = SentenceTransformer(self.model_name)
            logger.info(f"Loaded model: {self.model_name}")
        except Exception as e:
            logger.error(f"Error loading model {self.model_name}: {e}")
            raise
    
    def create_feedback_embedding(self, teacher_feedback: TeacherFeedback) -> Optional[AlignmentEmbedding]:
        """
        Create an embedding for teacher feedback.
        
        Args:
            teacher_feedback: TeacherFeedback instance
            
        Returns:
            AlignmentEmbedding instance or None if error
        """
        if not self.model:
            self.load_model()
        
        try:
            # Get alignment request and related data
            alignment_request = teacher_feedback.alignment_request
            question = alignment_request.question
            part = alignment_request.part
            topic = question.topic
            
            # Create context text combining question, student answer, and expected answer
            context_parts = []
            
            # Add question context
            if question.title:
                context_parts.append(f"Question: {question.title}")
            if question.description:
                context_parts.append(f"Description: {question.description[:200]}")
            if topic:
                context_parts.append(f"Topic: {topic.name}")
            
            # Add part context
            context_parts.append(f"Part: {part.description}")
            if part.answer:
                context_parts.append(f"Expected Answer: {part.answer}")
            
            # Add student answer
            context_parts.append(f"Student Answer: {alignment_request.student_answer}")
            
            # Add AI decision
            ai_decision = alignment_request.get_ai_decision()
            if ai_decision:
                context_parts.append(f"AI Score: {ai_decision.get('score', 'N/A')}/{part.score}")
                if ai_decision.get('reasoning'):
                    context_parts.append(f"AI Reasoning: {ai_decision['reasoning'][:200]}")
            
            context_text = " | ".join(context_parts)
            
            # Create feedback text combining teacher reasoning and guidance
            feedback_parts = [teacher_feedback.teacher_reasoning]
            if teacher_feedback.general_guidance:
                feedback_parts.append(teacher_feedback.general_guidance)
            if teacher_feedback.corrected_score is not None:
                feedback_parts.append(f"Corrected Score: {teacher_feedback.corrected_score}/{part.score}")
            
            feedback_text = " | ".join(feedback_parts)
            
            # Combine context and feedback for embedding
            combined_text = f"{context_text} || {feedback_text}"
            
            # Generate embedding
            embedding = self.model.encode([combined_text])
            embedding = normalize(embedding, norm='l2').astype('float32')[0]
            
            # Create AlignmentEmbedding record
            alignment_embedding = AlignmentEmbedding(
                teacher_feedback_id=teacher_feedback.id,
                model_name=self.model_name,
                vector_dimension=len(embedding),
                context_text=context_text,
                feedback_text=feedback_text,
                question_topic=topic.name if topic else None,
                feedback_type=teacher_feedback.feedback_type,
                severity=teacher_feedback.severity
            )
            alignment_embedding.set_vector(embedding)
            
            return alignment_embedding
            
        except Exception as e:
            logger.error(f"Error creating embedding for feedback {teacher_feedback.id}: {e}")
            return None
    
    def process_pending_feedback(self, force_recreate: bool = False) -> int:
        """
        Process all teacher feedback that doesn't have embeddings yet.
        
        Args:
            force_recreate: Whether to recreate existing embeddings
            
        Returns:
            Number of embeddings created
        """
        with app.app_context():
            # Get feedback without embeddings or all if force_recreate
            if force_recreate:
                feedback_query = TeacherFeedback.query
            else:
                feedback_query = TeacherFeedback.query.filter(
                    ~TeacherFeedback.id.in_(
                        db.session.query(AlignmentEmbedding.teacher_feedback_id).filter(
                            AlignmentEmbedding.model_name == self.model_name
                        )
                    )
                )
            
            feedback_list = feedback_query.all()
            
            if not feedback_list:
                logger.info("No pending feedback to process")
                return 0
            
            logger.info(f"Processing {len(feedback_list)} feedback items")
            
            processed = 0
            for feedback in feedback_list:
                try:
                    # Delete existing embedding if force_recreate
                    if force_recreate:
                        existing = AlignmentEmbedding.query.filter_by(
                            teacher_feedback_id=feedback.id,
                            model_name=self.model_name
                        ).first()
                        if existing:
                            db.session.delete(existing)
                    
                    # Create new embedding
                    embedding = self.create_feedback_embedding(feedback)
                    if embedding:
                        db.session.add(embedding)
                        processed += 1
                        
                        if processed % 10 == 0:
                            db.session.commit()
                            logger.info(f"Processed {processed} embeddings")
                            
                except Exception as e:
                    logger.error(f"Error processing feedback {feedback.id}: {e}")
                    db.session.rollback()
                    continue
            
            db.session.commit()
            logger.info(f"Successfully processed {processed} feedback embeddings")
            return processed
    
    def build_faiss_index(self) -> bool:
        """
        Build FAISS index from existing embeddings.
        
        Returns:
            True if successful, False otherwise
        """
        with app.app_context():
            embeddings = AlignmentEmbedding.query.filter_by(model_name=self.model_name).all()
            
            if not embeddings:
                logger.warning("No embeddings found to build index")
                return False
            
            # Extract vectors and create mapping
            vectors = []
            self.index_to_feedback_id = {}
            
            for i, embedding in enumerate(embeddings):
                vector = embedding.get_vector()
                vectors.append(vector)
                self.index_to_feedback_id[i] = embedding.teacher_feedback_id
            
            # Create FAISS index
            vectors = np.array(vectors).astype('float32')
            dimension = vectors.shape[1]
            
            self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner product for cosine similarity
            self.faiss_index.add(vectors)
            
            logger.info(f"Built FAISS index with {len(vectors)} embeddings")
            return True
    
    def search_similar_feedback(self, query_text: str, top_k: int = 5, 
                              min_score: float = 0.3, 
                              feedback_type_filter: Optional[str] = None,
                              severity_filter: Optional[str] = None,
                              topic_filter: Optional[str] = None) -> List[Dict]:
        """
        Search for similar teacher feedback based on query text.
        
        Args:
            query_text: Text to search for similar feedback
            top_k: Number of results to return
            min_score: Minimum similarity score threshold
            feedback_type_filter: Filter by feedback type
            severity_filter: Filter by severity
            topic_filter: Filter by topic
            
        Returns:
            List of dictionaries with feedback information and scores
        """
        if not self.model:
            self.load_model()
        
        if not self.faiss_index:
            if not self.build_faiss_index():
                return []
        
        # Generate query embedding
        query_embedding = self.model.encode([query_text])
        query_embedding = normalize(query_embedding, norm='l2').astype('float32')
        
        # Search FAISS index
        scores, indices = self.faiss_index.search(query_embedding, top_k * 2)  # Get extra to filter
        
        results = []
        with app.app_context():
            for score, index in zip(scores[0], indices[0]):
                if score < min_score:
                    continue
                
                feedback_id = self.index_to_feedback_id.get(index)
                if not feedback_id:
                    continue
                
                # Get feedback and embedding from database
                feedback = TeacherFeedback.query.get(feedback_id)
                embedding = AlignmentEmbedding.query.filter_by(
                    teacher_feedback_id=feedback_id,
                    model_name=self.model_name
                ).first()
                
                if not feedback or not embedding:
                    continue
                
                # Apply filters
                if feedback_type_filter and feedback.feedback_type != feedback_type_filter:
                    continue
                if severity_filter and feedback.severity != severity_filter:
                    continue
                if topic_filter and embedding.question_topic != topic_filter:
                    continue
                
                result = {
                    'feedback': feedback.to_dict(),
                    'embedding': embedding.to_dict(),
                    'similarity_score': float(score),
                    'relevance_type': self._get_relevance_type(score)
                }
                results.append(result)
        
        # Sort by score and limit results
        results.sort(key=lambda x: x['similarity_score'], reverse=True)
        return results[:top_k]
    
    def _get_relevance_type(self, score: float) -> str:
        """
        Determine relevance type based on similarity score.
        
        Args:
            score: Similarity score (0-1)
            
        Returns:
            Relevance type string
        """
        if score >= 0.8:
            return "highly_relevant"
        elif score >= 0.6:
            return "relevant"
        elif score >= 0.4:
            return "somewhat_relevant"
        else:
            return "marginally_relevant"


def main():
    """Main function for command-line usage."""
    import argparse
    
    parser = argparse.ArgumentParser(description='RLHF Alignment RAG System')
    parser.add_argument('--process-feedback', action='store_true',
                       help='Process pending teacher feedback into embeddings')
    parser.add_argument('--force-recreate', action='store_true',
                       help='Force recreate all embeddings')
    parser.add_argument('--build-index', action='store_true',
                       help='Build FAISS index from existing embeddings')
    parser.add_argument('--search', type=str,
                       help='Search for similar feedback')
    parser.add_argument('--top-k', type=int, default=5,
                       help='Number of search results to return')
    
    args = parser.parse_args()
    
    rag_system = AlignmentRAGSystem()
    
    if args.process_feedback:
        count = rag_system.process_pending_feedback(force_recreate=args.force_recreate)
        print(f"Processed {count} feedback embeddings")
    
    if args.build_index:
        success = rag_system.build_faiss_index()
        print(f"Index building {'successful' if success else 'failed'}")
    
    if args.search:
        results = rag_system.search_similar_feedback(args.search, top_k=args.top_k)
        print(f"Found {len(results)} similar feedback items:")
        for i, result in enumerate(results, 1):
            print(f"\n{i}. Score: {result['similarity_score']:.3f}")
            print(f"   Type: {result['feedback']['feedback_type']}")
            print(f"   Reasoning: {result['feedback']['teacher_reasoning'][:100]}...")


if __name__ == "__main__":
    main()
