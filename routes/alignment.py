"""
RLHF Alignment System Routes
Handles teacher feedback collection and alignment request management.
"""

import os
import json
from datetime import datetime
from flask import render_template, request, redirect, url_for, flash, session, jsonify
from models import (db, AlignmentRequest, TeacherFeedback, AlignmentEmbedding,
                   Submission, Question, Part, MarkingPoint, User, Topic)
from .utils import admin_required, login_required, app_logger, error_logger


def create_feedback_embedding_async(feedback_id: int):
    """
    Create embedding for teacher feedback asynchronously.
    This can be called after feedback submission to avoid blocking the response.
    """
    try:
        from alignment_rag_system import AlignmentRAGSystem

        rag_system = AlignmentRAGSystem()

        with db.session.begin():
            feedback = TeacherFeedback.query.get(feedback_id)
            if not feedback:
                app_logger.error(f"Feedback {feedback_id} not found for embedding creation")
                return

            # Check if embedding already exists
            existing_embedding = AlignmentEmbedding.query.filter_by(
                teacher_feedback_id=feedback_id,
                model_name=rag_system.model_name
            ).first()

            if existing_embedding:
                app_logger.info(f"Embedding already exists for feedback {feedback_id}")
                return

            # Create embedding
            embedding = rag_system.create_feedback_embedding(feedback)
            if embedding:
                db.session.add(embedding)
                app_logger.info(f"Created embedding for feedback {feedback_id}")
            else:
                app_logger.error(f"Failed to create embedding for feedback {feedback_id}")

    except Exception as e:
        error_logger.exception(f"Error creating embedding for feedback {feedback_id}: {e}")


def register_alignment_routes(app, db, session):
    """Register all alignment system routes."""
    
    @app.route("/admin/alignment", methods=["GET"])
    @admin_required
    def admin_alignment():
        """Admin dashboard for reviewing alignment requests."""
        try:
            # Get filter parameters
            status_filter = request.args.get('status', 'pending')
            priority_filter = request.args.get('priority', 'all')
            page = int(request.args.get('page', 1))
            per_page = 20
            
            # Build query
            query = AlignmentRequest.query
            
            # Apply filters
            if status_filter != 'all':
                query = query.filter(AlignmentRequest.status == status_filter)
            
            if priority_filter == 'high':
                query = query.filter(AlignmentRequest.priority_score >= 0.7)
            elif priority_filter == 'medium':
                query = query.filter(AlignmentRequest.priority_score.between(0.3, 0.7))
            elif priority_filter == 'low':
                query = query.filter(AlignmentRequest.priority_score < 0.3)
            
            # Order by priority and creation date
            query = query.order_by(
                AlignmentRequest.priority_score.desc(),
                AlignmentRequest.created_at.desc()
            )
            
            # Paginate
            alignment_requests = query.paginate(
                page=page, per_page=per_page, error_out=False
            )
            
            # Get statistics
            stats = {
                'total_pending': AlignmentRequest.query.filter_by(status='pending').count(),
                'total_reviewed': AlignmentRequest.query.filter_by(status='reviewed').count(),
                'high_priority': AlignmentRequest.query.filter(
                    AlignmentRequest.status == 'pending',
                    AlignmentRequest.priority_score >= 0.7
                ).count()
            }
            
            return render_template(
                "admin_alignment.html",
                title="Alignment Review",
                alignment_requests=alignment_requests,
                stats=stats,
                status_filter=status_filter,
                priority_filter=priority_filter
            )
            
        except Exception as e:
            error_logger.exception("Error loading alignment dashboard")
            flash("Error loading alignment dashboard.", "error")
            return redirect(url_for('admin'))
    
    @app.route("/admin/alignment/<int:request_id>", methods=["GET"])
    @admin_required
    def alignment_request_detail(request_id):
        """View detailed alignment request for teacher review."""
        try:
            alignment_request = AlignmentRequest.query.get_or_404(request_id)
            
            # Get related data
            submission = alignment_request.submission
            question = alignment_request.question
            part = alignment_request.part
            marking_point = alignment_request.marking_point
            
            # Get AI decision details
            ai_decision = alignment_request.get_ai_decision()
            
            # Get any existing teacher feedback
            existing_feedback = TeacherFeedback.query.filter_by(
                alignment_request_id=request_id
            ).first()
            
            # Get question context (topic, subject)
            topic = question.topic if question.topic else None
            subject = topic.subject if topic else None
            
            return render_template(
                "alignment_request_detail.html",
                title=f"Review Request #{request_id}",
                alignment_request=alignment_request,
                submission=submission,
                question=question,
                part=part,
                marking_point=marking_point,
                ai_decision=ai_decision,
                existing_feedback=existing_feedback,
                topic=topic,
                subject=subject
            )
            
        except Exception as e:
            error_logger.exception(f"Error loading alignment request {request_id}")
            flash("Error loading alignment request.", "error")
            return redirect(url_for('admin_alignment'))
    
    @app.route("/admin/alignment/<int:request_id>/feedback", methods=["POST"])
    @admin_required
    def submit_teacher_feedback(request_id):
        """Submit teacher feedback for an alignment request."""
        try:
            alignment_request = AlignmentRequest.query.get_or_404(request_id)
            
            # Get form data
            corrected_score = request.form.get('corrected_score')
            teacher_reasoning = request.form.get('teacher_reasoning', '').strip()
            feedback_type = request.form.get('feedback_type', 'score_correction')
            severity = request.form.get('severity', 'medium')
            general_guidance = request.form.get('general_guidance', '').strip()
            tags_input = request.form.get('tags', '').strip()
            
            # Validate required fields
            if not teacher_reasoning:
                flash("Teacher reasoning is required.", "error")
                return redirect(url_for('alignment_request_detail', request_id=request_id))
            
            # Parse corrected score
            if corrected_score:
                try:
                    corrected_score = float(corrected_score)
                except ValueError:
                    flash("Invalid score format.", "error")
                    return redirect(url_for('alignment_request_detail', request_id=request_id))
            else:
                corrected_score = None
            
            # Parse tags
            tags = [tag.strip() for tag in tags_input.split(',') if tag.strip()] if tags_input else []
            
            # Check if feedback already exists
            existing_feedback = TeacherFeedback.query.filter_by(
                alignment_request_id=request_id
            ).first()
            
            if existing_feedback:
                # Update existing feedback
                existing_feedback.corrected_score = corrected_score
                existing_feedback.teacher_reasoning = teacher_reasoning
                existing_feedback.feedback_type = feedback_type
                existing_feedback.severity = severity
                existing_feedback.general_guidance = general_guidance
                existing_feedback.set_tags(tags)
                existing_feedback.updated_at = datetime.utcnow()
                
                teacher_feedback = existing_feedback
                action = "updated"
            else:
                # Create new feedback
                teacher_feedback = TeacherFeedback(
                    alignment_request_id=request_id,
                    teacher_id=session['user_id'],
                    corrected_score=corrected_score,
                    teacher_reasoning=teacher_reasoning,
                    feedback_type=feedback_type,
                    severity=severity,
                    general_guidance=general_guidance
                )
                teacher_feedback.set_tags(tags)
                db.session.add(teacher_feedback)
                action = "submitted"
            
            # Update alignment request status
            alignment_request.status = 'reviewed'
            alignment_request.reviewed_at = datetime.utcnow()
            
            db.session.commit()

            # Create embedding for the feedback asynchronously
            try:
                create_feedback_embedding_async(teacher_feedback.id)
            except Exception as e:
                # Don't fail the main operation if embedding creation fails
                error_logger.exception(f"Error creating embedding for feedback {teacher_feedback.id}: {e}")

            app_logger.info(f"Teacher feedback {action} for alignment request {request_id} by user {session['user_id']}")
            flash(f"Feedback {action} successfully!", "success")
            
            # Redirect to next pending request or back to dashboard
            next_request = AlignmentRequest.query.filter_by(status='pending').order_by(
                AlignmentRequest.priority_score.desc(),
                AlignmentRequest.created_at.desc()
            ).first()
            
            if next_request:
                return redirect(url_for('alignment_request_detail', request_id=next_request.id))
            else:
                return redirect(url_for('admin_alignment'))
                
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error submitting teacher feedback for request {request_id}")
            flash("Error submitting feedback.", "error")
            return redirect(url_for('alignment_request_detail', request_id=request_id))
    
    @app.route("/admin/alignment/<int:request_id>/dismiss", methods=["POST"])
    @admin_required
    def dismiss_alignment_request(request_id):
        """Dismiss an alignment request without providing feedback."""
        try:
            alignment_request = AlignmentRequest.query.get_or_404(request_id)
            
            alignment_request.status = 'dismissed'
            alignment_request.reviewed_at = datetime.utcnow()
            
            db.session.commit()
            
            app_logger.info(f"Alignment request {request_id} dismissed by user {session['user_id']}")
            flash("Request dismissed successfully!", "success")
            
            return redirect(url_for('admin_alignment'))
            
        except Exception as e:
            db.session.rollback()
            error_logger.exception(f"Error dismissing alignment request {request_id}")
            flash("Error dismissing request.", "error")
            return redirect(url_for('alignment_request_detail', request_id=request_id))
    
    @app.route("/api/alignment/stats", methods=["GET"])
    @admin_required
    def alignment_stats_api():
        """API endpoint for alignment system statistics."""
        try:
            stats = {
                'total_requests': AlignmentRequest.query.count(),
                'pending_requests': AlignmentRequest.query.filter_by(status='pending').count(),
                'reviewed_requests': AlignmentRequest.query.filter_by(status='reviewed').count(),
                'dismissed_requests': AlignmentRequest.query.filter_by(status='dismissed').count(),
                'high_priority_pending': AlignmentRequest.query.filter(
                    AlignmentRequest.status == 'pending',
                    AlignmentRequest.priority_score >= 0.7
                ).count(),
                'feedback_count': TeacherFeedback.query.count(),
                'embedding_count': AlignmentEmbedding.query.count()
            }
            
            return jsonify(stats)
            
        except Exception as e:
            error_logger.exception("Error getting alignment stats")
            return jsonify({'error': 'Failed to get stats'}), 500

    @app.route("/admin/alignment/analytics", methods=["GET"])
    @admin_required
    def alignment_analytics():
        """Analytics and monitoring dashboard for the alignment system."""
        try:
            from feedback_processing_system import FeedbackProcessor

            # Get time period from query params
            days_back = int(request.args.get('days', 30))

            processor = FeedbackProcessor()

            # Generate comprehensive report
            report = processor.generate_alignment_report(days_back)

            # Get additional metrics
            with db.session.begin():
                # Trigger reason distribution
                trigger_reasons = db.session.query(
                    AlignmentRequest.trigger_reason,
                    db.func.count(AlignmentRequest.id).label('count')
                ).group_by(AlignmentRequest.trigger_reason).all()

                # Priority distribution
                priority_ranges = [
                    ('High (≥0.7)', AlignmentRequest.query.filter(AlignmentRequest.priority_score >= 0.7).count()),
                    ('Medium (0.3-0.7)', AlignmentRequest.query.filter(
                        AlignmentRequest.priority_score.between(0.3, 0.7)
                    ).count()),
                    ('Low (<0.3)', AlignmentRequest.query.filter(AlignmentRequest.priority_score < 0.3).count())
                ]

                # Recent activity (last 7 days)
                recent_cutoff = datetime.utcnow() - timedelta(days=7)
                recent_requests = AlignmentRequest.query.filter(
                    AlignmentRequest.created_at >= recent_cutoff
                ).count()
                recent_feedback = TeacherFeedback.query.filter(
                    TeacherFeedback.created_at >= recent_cutoff
                ).count()

                # Top teachers by feedback count
                top_teachers = db.session.query(
                    User.username,
                    db.func.count(TeacherFeedback.id).label('feedback_count')
                ).join(TeacherFeedback, User.id == TeacherFeedback.teacher_id)\
                 .group_by(User.id, User.username)\
                 .order_by(db.func.count(TeacherFeedback.id).desc())\
                 .limit(5).all()

            analytics_data = {
                'report': report,
                'trigger_reasons': [{'reason': reason, 'count': count} for reason, count in trigger_reasons],
                'priority_distribution': [{'range': range_name, 'count': count} for range_name, count in priority_ranges],
                'recent_activity': {
                    'requests': recent_requests,
                    'feedback': recent_feedback
                },
                'top_teachers': [{'username': username, 'feedback_count': count} for username, count in top_teachers],
                'days_back': days_back
            }

            return render_template(
                "alignment_analytics.html",
                title="Alignment Analytics",
                analytics=analytics_data
            )

        except Exception as e:
            error_logger.exception("Error loading alignment analytics")
            flash("Error loading analytics dashboard.", "error")
            return redirect(url_for('admin_alignment'))

    @app.route("/admin/alignment/process-feedback", methods=["POST"])
    @admin_required
    def process_feedback_batch():
        """Process pending feedback into embeddings."""
        try:
            from feedback_processing_system import FeedbackProcessor

            processor = FeedbackProcessor()
            results = processor.process_feedback_batch()

            if results['errors']:
                flash(f"Processing completed with errors: {', '.join(results['errors'])}", "warning")
            else:
                flash(f"Successfully processed {results['processed_count']} feedback items and created {results['embedding_count']} embeddings.", "success")

            return redirect(url_for('alignment_analytics'))

        except Exception as e:
            error_logger.exception("Error processing feedback batch")
            flash("Error processing feedback batch.", "error")
            return redirect(url_for('alignment_analytics'))

    @app.route("/admin/alignment/optimize", methods=["POST"])
    @admin_required
    def optimize_alignment_system():
        """Optimize the alignment RAG system."""
        try:
            from feedback_processing_system import FeedbackProcessor

            processor = FeedbackProcessor()
            results = processor.optimize_rag_system()

            if results['actions_taken']:
                flash(f"Optimization completed: {', '.join(results['actions_taken'])}", "success")

            if results['recommendations']:
                flash(f"Recommendations: {', '.join(results['recommendations'])}", "info")

            return redirect(url_for('alignment_analytics'))

        except Exception as e:
            error_logger.exception("Error optimizing alignment system")
            flash("Error optimizing alignment system.", "error")
            return redirect(url_for('alignment_analytics'))
