#!/usr/bin/env python3
"""
RLHF Alignment System Setup Script
Handles installation, database migration, and initial configuration.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def run_command(command, description=""):
    """Run a shell command and handle errors."""
    logger.info(f"Running: {description or command}")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        if result.stdout:
            logger.info(f"Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running command: {command}")
        logger.error(f"Error output: {e.stderr}")
        return False


def check_dependencies():
    """Check if required dependencies are installed."""
    logger.info("Checking dependencies...")
    
    required_packages = [
        'flask',
        'sqlalchemy',
        'alembic'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.error("Please install them with: pip install flask sqlalchemy alembic")
        return False
    
    logger.info("Core dependencies are installed")
    return True


def install_alignment_dependencies():
    """Install alignment system specific dependencies."""
    logger.info("Installing alignment system dependencies...")
    
    requirements_file = Path("requirements_alignment.txt")
    if not requirements_file.exists():
        logger.error("requirements_alignment.txt not found")
        return False
    
    return run_command(
        f"pip install -r {requirements_file}",
        "Installing alignment system dependencies"
    )


def run_database_migration():
    """Run database migration to create alignment tables."""
    logger.info("Running database migration...")
    
    # Check if migration file exists
    migration_file = Path("migrations/versions/add_rlhf_alignment_tables.py")
    if not migration_file.exists():
        logger.error("Migration file not found")
        return False
    
    # Run the migration
    return run_command(
        "python manage_db.py upgrade",
        "Running database migration"
    )


def initialize_rag_system():
    """Initialize the RAG system."""
    logger.info("Initializing RAG system...")
    
    try:
        from alignment_rag_system import AlignmentRAGSystem
        
        rag_system = AlignmentRAGSystem()
        
        # Load the model to ensure it's available
        rag_system.load_model()
        logger.info("RAG system model loaded successfully")
        
        # Try to build an empty index
        rag_system.build_faiss_index()
        logger.info("FAISS index initialized")
        
        return True
        
    except Exception as e:
        logger.error(f"Error initializing RAG system: {e}")
        return False


def create_sample_data():
    """Create some sample alignment requests for testing."""
    logger.info("Creating sample data...")
    
    try:
        # Add the current directory to Python path
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app import app
        from models import db, AlignmentRequest, Question, Part, Submission, User
        
        with app.app_context():
            # Check if we already have sample data
            existing_requests = AlignmentRequest.query.count()
            if existing_requests > 0:
                logger.info(f"Sample data already exists ({existing_requests} alignment requests)")
                return True
            
            # Get a sample question and part for testing
            sample_question = Question.query.first()
            sample_part = Part.query.first()
            sample_user = User.query.first()
            
            if not all([sample_question, sample_part, sample_user]):
                logger.warning("No sample questions/parts/users found - skipping sample data creation")
                return True
            
            # Create a sample submission
            sample_submission = Submission(
                user_id=sample_user.id,
                question_id=sample_question.id,
                part_id=sample_part.id,
                answer="This is a sample student answer for testing the alignment system.",
                score=2.5,
                feedback='{"score": 2.5, "evaluated_points": []}'
            )
            db.session.add(sample_submission)
            db.session.flush()  # Get the ID
            
            # Create a sample alignment request
            sample_request = AlignmentRequest(
                submission_id=sample_submission.id,
                question_id=sample_question.id,
                part_id=sample_part.id,
                student_answer="This is a sample student answer for testing the alignment system.",
                expected_answer=sample_part.answer or "Sample expected answer",
                question_context=f"{sample_question.title}: {sample_part.description}",
                status='pending',
                priority_score=0.7,
                trigger_reason='system_setup_sample'
            )
            
            sample_request.set_ai_decision({
                'score': 2.5,
                'max_score': sample_part.score,
                'reasoning': 'Sample AI reasoning for testing purposes',
                'confidence': 0.8
            })
            
            db.session.add(sample_request)
            db.session.commit()
            
            logger.info("Sample alignment request created successfully")
            return True
            
    except Exception as e:
        logger.error(f"Error creating sample data: {e}")
        return False


def verify_installation():
    """Verify that the alignment system is properly installed."""
    logger.info("Verifying installation...")
    
    try:
        # Add the current directory to Python path
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app import app
        from models import db, AlignmentRequest, TeacherFeedback, AlignmentEmbedding
        from alignment_rag_system import AlignmentRAGSystem
        from feedback_processing_system import FeedbackProcessor
        
        with app.app_context():
            # Check database tables
            tables_exist = True
            for model in [AlignmentRequest, TeacherFeedback, AlignmentEmbedding]:
                try:
                    model.query.count()
                except Exception as e:
                    logger.error(f"Table for {model.__name__} not accessible: {e}")
                    tables_exist = False
            
            if not tables_exist:
                logger.error("Database tables are not properly created")
                return False
            
            # Check RAG system
            rag_system = AlignmentRAGSystem()
            rag_system.load_model()
            
            # Check feedback processor
            processor = FeedbackProcessor()
            
            logger.info("All components verified successfully")
            return True
            
    except Exception as e:
        logger.error(f"Verification failed: {e}")
        return False


def main():
    """Main setup function."""
    logger.info("Starting RLHF Alignment System setup...")
    
    steps = [
        ("Checking dependencies", check_dependencies),
        ("Installing alignment dependencies", install_alignment_dependencies),
        ("Running database migration", run_database_migration),
        ("Initializing RAG system", initialize_rag_system),
        ("Creating sample data", create_sample_data),
        ("Verifying installation", verify_installation)
    ]
    
    failed_steps = []
    
    for step_name, step_function in steps:
        logger.info(f"\n{'='*50}")
        logger.info(f"Step: {step_name}")
        logger.info(f"{'='*50}")
        
        if not step_function():
            failed_steps.append(step_name)
            logger.error(f"Step failed: {step_name}")
        else:
            logger.info(f"Step completed: {step_name}")
    
    logger.info(f"\n{'='*50}")
    logger.info("Setup Summary")
    logger.info(f"{'='*50}")
    
    if failed_steps:
        logger.error(f"Setup completed with {len(failed_steps)} failed steps:")
        for step in failed_steps:
            logger.error(f"  - {step}")
        logger.error("\nPlease review the errors above and run the setup again.")
        return False
    else:
        logger.info("✅ RLHF Alignment System setup completed successfully!")
        logger.info("\nNext steps:")
        logger.info("1. Start your Flask application")
        logger.info("2. Navigate to /admin/alignment to access the alignment dashboard")
        logger.info("3. Review alignment requests and provide teacher feedback")
        logger.info("4. Monitor system performance at /admin/alignment/analytics")
        return True


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='RLHF Alignment System Setup')
    parser.add_argument('--skip-deps', action='store_true',
                       help='Skip dependency installation')
    parser.add_argument('--skip-migration', action='store_true',
                       help='Skip database migration')
    parser.add_argument('--skip-sample-data', action='store_true',
                       help='Skip sample data creation')
    
    args = parser.parse_args()
    
    # Modify steps based on arguments
    if args.skip_deps:
        logger.info("Skipping dependency installation")
    if args.skip_migration:
        logger.info("Skipping database migration")
    if args.skip_sample_data:
        logger.info("Skipping sample data creation")
    
    success = main()
    sys.exit(0 if success else 1)
